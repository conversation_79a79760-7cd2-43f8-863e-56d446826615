package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/enhance"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func main() {
	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		tenantID        = flag.String("tenant", "0R8Da4gBoELr5xpoQ6Y3", "TenantId to run enhancer for")
		lastCollectedAt = flag.String("collected", "1756468131048", "Last scan time for tenantId")
		serviceID       = flag.String("serviceId", "1000", "ServiceId of the service")
		debug           = flag.Bool("debug", false, "Debug mode")
	)

	flag.Parse()

	enableProfiling := true

	logger.InitializeLogs("enhancer", *debug)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	} else if len(*lastCollectedAt) <= 0 {
		logger.Print(logger.ERROR, "Last collected not specified")
		os.Exit(1)
	}

	var profiler *enhance.MemoryProfiler
	if enableProfiling {
		profiler = enhance.NewMemoryProfiler(*tenantID, *lastCollectedAt, *serviceID)
		profiler.RecordStage("startup")
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if enableProfiling {
		profiler.RecordStage("config_loaded")
		profiler.SaveHeapProfile("config_loaded")
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	if enableProfiling {
		profiler.RecordStage("elastic_connected")
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	if enableProfiling {
		profiler.RecordStage("services_initialized")
		profiler.SaveHeapProfile("services_initialized")
	}

	gracefullyShutDown()

	if enableProfiling {
		profiler.RecordStage("before_processing")
		profiler.SaveHeapProfile("before_processing")

		enhance.StartContextProcessing(*tenantID, *lastCollectedAt, *serviceID, *debug, profiler)

		profiler.RecordStage("processing_completed")
		profiler.SaveHeapProfile("processing_completed")

		profiler.GenerateReport()

	} else {
		enhance.StartContextProcessing(*tenantID, *lastCollectedAt, *serviceID, *debug, profiler)
	}

	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
