2025/08/30 15:02:04 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 8209 KB - GC: 0 - Objects: 2499 - Duration: 51.584µs - 
2025/08/30 15:02:04 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:02:04 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 8209 KB - GC: 0 - Objects: 2580 - Duration: 1.016833ms - 
2025/08/30 15:02:04 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:02:04 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:02:04 Index iac_git_commits exists - 
2025/08/30 15:02:04 Index cfstack_templates exists - 
2025/08/30 15:02:04 Index arm_templates exists - 
2025/08/30 15:02:05 Index terraform_resources exists - 
2025/08/30 15:02:05 Index tf_commits exists - 
2025/08/30 15:02:05 Index tf_variables exists - 
2025/08/30 15:02:05 Index resource_context exists - 
2025/08/30 15:02:05 Index text_lookup exists - 
2025/08/30 15:02:05 Index ai_resources exists - 
2025/08/30 15:02:06 Index idp_events exists - 
2025/08/30 15:02:06 Index idp_users exists - 
2025/08/30 15:02:06 Index idp_apps exists - 
2025/08/30 15:02:06 Index idp_groups exists - 
2025/08/30 15:02:06 Index cloud_incidents exists - 
2025/08/30 15:02:06 Index jira_issues exists - 
2025/08/30 15:02:07 Index jira_data exists - 
2025/08/30 15:02:07 Index jira_resources exists - 
2025/08/30 15:02:07 Index precize_creations exists - 
2025/08/30 15:02:07 Index external_cloud_resources exists - 
2025/08/30 15:02:07 Memory Stage: elastic_connected - Alloc: 2087 KB - Total: 2251 KB - Sys: 13393 KB - GC: 1 - Objects: 3836 - Duration: 3.570442833s - 
2025/08/30 15:02:07 Memory Stage: services_initialized - Alloc: 2089 KB - Total: 2254 KB - Sys: 13393 KB - GC: 1 - Objects: 3880 - Duration: 306.5µs - 
2025/08/30 15:02:07 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:02:07 Memory Stage: before_processing - Alloc: 1865 KB - Total: 3487 KB - Sys: 13521 KB - GC: 2 - Objects: 2519 - Duration: 5.349334ms - 
2025/08/30 15:02:07 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:02:07 Memory Stage: service_id_resolved - Alloc: 1836 KB - Total: 4721 KB - Sys: 13521 KB - GC: 3 - Objects: 2512 - Duration: 2.017625ms - 
2025/08/30 15:02:07 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:02:07 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:02:12 Memory Stage: tenant_data_loaded - Alloc: 2058 KB - Total: 4943 KB - Sys: 13521 KB - GC: 3 - Objects: 4157 - Duration: 4.403512666s - 
2025/08/30 15:02:12 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:02:12 Previous collected at - 1756463109875 - 
2025/08/30 15:02:13 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:02:13 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:02:13 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:02:15 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:02:15 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:02:15 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:02:15 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:02:15 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:02:16 Memory Stage: context_initialized - Alloc: 2479 KB - Total: 10599 KB - Sys: 14033 KB - GC: 6 - Objects: 14571 - Duration: 4.57686025s - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:02:16 Memory Stage: jira_context_loaded - Alloc: 2561 KB - Total: 10682 KB - Sys: 14033 KB - GC: 6 - Objects: 15046 - Duration: 64.482042ms - 
2025/08/30 15:02:16 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:02:16 [10] Resources fetched - 1 - 
2025/08/30 15:02:17 [10] Email Status Request - map[<EMAIL>:Saiashish] - 
2025/08/30 15:02:17 [10] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:02:17 [10] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:02:17 [10] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:02:17 [10] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:02:17 [10] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:02:18 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:02:18 Memory Stage: resource_context_loaded - Alloc: 2084 KB - Total: 14946 KB - Sys: 14033 KB - GC: 8 - Objects: 13055 - Duration: 1.93294475s - 
2025/08/30 15:02:18 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:02:18 Memory Stage: human_evaluation_initialized - Alloc: 2384 KB - Total: 16353 KB - Sys: 14033 KB - GC: 9 - Objects: 6357 - Duration: 6.078625ms - 
2025/08/30 15:02:19 Memory Stage: sync_maps_initialized - Alloc: 2319 KB - Total: 18518 KB - Sys: 14033 KB - GC: 10 - Objects: 13727 - Duration: 1.147281917s - 
2025/08/30 15:02:19 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:02:19 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:02:19 <EMAIL> - UserContext:  Name: Saiashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:02:19 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:02:19 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:02:19 Memory Stage: stage1_start - Alloc: 2344 KB - Total: 18542 KB - Sys: 14033 KB - GC: 10 - Objects: 14088 - Duration: 1.878208ms - 
2025/08/30 15:02:19 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:02:19 Memory Stage: stage1_completed - Alloc: 2419 KB - Total: 19968 KB - Sys: 14033 KB - GC: 11 - Objects: 6893 - Duration: 4.51025ms - 
2025/08/30 15:02:19 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:02:19 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:02:24 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:02:24 Memory Stage: stage2_start - Alloc: 899 KB - Total: 21349 KB - Sys: 14545 KB - GC: 13 - Objects: 5957 - Duration: 5.005917292s - 
2025/08/30 15:02:24 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:04:12 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 8465 KB - GC: 0 - Objects: 2495 - Duration: 72.625µs - 
2025/08/30 15:04:12 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:04:12 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 8465 KB - GC: 0 - Objects: 2575 - Duration: 824.667µs - 
2025/08/30 15:04:12 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:04:17 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:04:19 Index iac_git_commits exists - 
2025/08/30 15:04:22 Index cfstack_templates exists - 
2025/08/30 15:04:24 Index arm_templates exists - 
2025/08/30 15:04:26 Index terraform_resources exists - 
2025/08/30 15:04:29 Index tf_commits exists - 
2025/08/30 15:04:30 Index tf_variables exists - 
2025/08/30 15:04:33 Index resource_context exists - 
2025/08/30 15:04:38 Index text_lookup exists - 
2025/08/30 15:04:41 Index ai_resources exists - 
2025/08/30 15:04:42 Index idp_events exists - 
2025/08/30 15:04:43 Index idp_users exists - 
2025/08/30 15:04:45 Index idp_apps exists - 
2025/08/30 15:04:47 Index idp_groups exists - 
2025/08/30 15:04:49 Index cloud_incidents exists - 
2025/08/30 15:04:50 Index jira_issues exists - 
2025/08/30 15:04:51 Index jira_data exists - 
2025/08/30 15:04:52 Index jira_resources exists - 
2025/08/30 15:04:53 Index precize_creations exists - 
2025/08/30 15:04:54 Index external_cloud_resources exists - 
2025/08/30 15:04:54 Memory Stage: elastic_connected - Alloc: 2103 KB - Total: 2268 KB - Sys: 13137 KB - GC: 1 - Objects: 3875 - Duration: 42.015660667s - 
2025/08/30 15:04:54 Memory Stage: services_initialized - Alloc: 2105 KB - Total: 2270 KB - Sys: 13137 KB - GC: 1 - Objects: 3919 - Duration: 267.208µs - 
2025/08/30 15:04:54 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:04:54 Memory Stage: before_processing - Alloc: 1871 KB - Total: 3506 KB - Sys: 13201 KB - GC: 2 - Objects: 2538 - Duration: 2.892583ms - 
2025/08/30 15:04:54 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:04:54 Memory Stage: service_id_resolved - Alloc: 1847 KB - Total: 4747 KB - Sys: 13201 KB - GC: 3 - Objects: 2544 - Duration: 3.119959ms - 
2025/08/30 15:04:54 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:04:54 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:05:01 Memory Stage: tenant_data_loaded - Alloc: 2069 KB - Total: 4968 KB - Sys: 13201 KB - GC: 3 - Objects: 4183 - Duration: 6.976779875s - 
2025/08/30 15:05:01 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:05:02 Previous collected at - 1756463109875 - 
2025/08/30 15:05:04 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:05:04 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:05:05 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:05:05 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:05:05 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:05:06 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:05:07 Memory Stage: context_initialized - Alloc: 2700 KB - Total: 10729 KB - Sys: 14225 KB - GC: 6 - Objects: 16464 - Duration: 5.940252708s - 
2025/08/30 15:05:07 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:05:12 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:05:12 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:05:17 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:05:20 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:05:20 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Sowmya Test] - 
2025/08/30 15:05:21 Email Status Response - map[<EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:05:21 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:05:21 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:05:24 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:05:24 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:05:25 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:05:25 Memory Stage: jira_context_loaded - Alloc: 18175 KB - Total: 145200 KB - Sys: 44497 KB - GC: 24 - Objects: 118607 - Duration: 18.139104125s - 
2025/08/30 15:05:25 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:05:25 [3] Resources fetched - 1 - 
2025/08/30 15:05:26 [3] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:05:26 [3] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:05:26 [3] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:05:26 [3] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:05:27 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:05:27 Memory Stage: resource_context_loaded - Alloc: 2676 KB - Total: 149283 KB - Sys: 44497 KB - GC: 25 - Objects: 12486 - Duration: 2.1021275s - 
2025/08/30 15:05:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:05:27 Memory Stage: human_evaluation_initialized - Alloc: 2566 KB - Total: 150799 KB - Sys: 44497 KB - GC: 26 - Objects: 7871 - Duration: 3.313875ms - 
2025/08/30 15:05:27 Memory Stage: sync_maps_initialized - Alloc: 1829 KB - Total: 152836 KB - Sys: 44497 KB - GC: 28 - Objects: 10586 - Duration: 460.944333ms - 
2025/08/30 15:05:27 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:05:27 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:05:27 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:05:27 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:05:27 Memory Stage: stage1_start - Alloc: 1168 KB - Total: 152889 KB - Sys: 44497 KB - GC: 28 - Objects: 7496 - Duration: 3.743875ms - 
2025/08/30 15:05:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:05:27 Memory Stage: stage1_completed - Alloc: 2527 KB - Total: 154483 KB - Sys: 44497 KB - GC: 29 - Objects: 8876 - Duration: 6.78375ms - 
2025/08/30 15:05:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:05:27 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:05:32 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:05:32 Memory Stage: stage2_start - Alloc: 990 KB - Total: 156046 KB - Sys: 44497 KB - GC: 31 - Objects: 6849 - Duration: 5.147763875s - 
2025/08/30 15:05:32 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:06:32 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 8465 KB - GC: 0 - Objects: 2498 - Duration: 63.417µs - 
2025/08/30 15:06:32 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:06:32 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 8465 KB - GC: 0 - Objects: 2578 - Duration: 856.083µs - 
2025/08/30 15:06:32 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:06:34 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:06:34 Index iac_git_commits exists - 
2025/08/30 15:06:34 Index cfstack_templates exists - 
2025/08/30 15:06:35 Index arm_templates exists - 
2025/08/30 15:06:35 Index terraform_resources exists - 
2025/08/30 15:06:35 Index tf_commits exists - 
2025/08/30 15:06:35 Index tf_variables exists - 
2025/08/30 15:06:35 Index resource_context exists - 
2025/08/30 15:06:35 Index text_lookup exists - 
2025/08/30 15:06:35 Index ai_resources exists - 
2025/08/30 15:06:36 Index idp_events exists - 
2025/08/30 15:06:36 Index idp_users exists - 
2025/08/30 15:06:36 Index idp_apps exists - 
2025/08/30 15:06:36 Index idp_groups exists - 
2025/08/30 15:06:37 Index cloud_incidents exists - 
2025/08/30 15:06:37 Index jira_issues exists - 
2025/08/30 15:06:37 Index jira_data exists - 
2025/08/30 15:06:37 Index jira_resources exists - 
2025/08/30 15:06:38 Index precize_creations exists - 
2025/08/30 15:06:38 Index external_cloud_resources exists - 
2025/08/30 15:06:38 Memory Stage: elastic_connected - Alloc: 2114 KB - Total: 2280 KB - Sys: 9041 KB - GC: 1 - Objects: 3929 - Duration: 5.369955042s - 
2025/08/30 15:06:38 Memory Stage: services_initialized - Alloc: 2117 KB - Total: 2282 KB - Sys: 9041 KB - GC: 1 - Objects: 3974 - Duration: 162.333µs - 
2025/08/30 15:06:38 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:06:38 Memory Stage: before_processing - Alloc: 1904 KB - Total: 3551 KB - Sys: 9105 KB - GC: 2 - Objects: 2613 - Duration: 1.943917ms - 
2025/08/30 15:06:38 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:06:38 Memory Stage: service_id_resolved - Alloc: 1875 KB - Total: 4814 KB - Sys: 9361 KB - GC: 3 - Objects: 2603 - Duration: 1.433083ms - 
2025/08/30 15:06:38 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:06:38 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:06:42 Memory Stage: tenant_data_loaded - Alloc: 2096 KB - Total: 5035 KB - Sys: 9361 KB - GC: 3 - Objects: 4239 - Duration: 4.375600125s - 
2025/08/30 15:06:42 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:06:42 Previous collected at - 1756463109875 - 
2025/08/30 15:06:43 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:06:43 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:06:43 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:06:44 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:06:45 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:06:46 Memory Stage: context_initialized - Alloc: 2542 KB - Total: 10757 KB - Sys: 13713 KB - GC: 6 - Objects: 14354 - Duration: 3.579710583s - 
2025/08/30 15:06:46 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:06:49 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:06:49 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:06:55 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:06:59 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:06:59 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Sai Ashish] - 
2025/08/30 15:06:59 Email Status Response - map[<EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:06:59 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:07:00 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:07:03 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:07:03 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:07:03 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:07:03 Memory Stage: jira_context_loaded - Alloc: 1240 KB - Total: 144137 KB - Sys: 44305 KB - GC: 25 - Objects: 6207 - Duration: 17.219519292s - 
2025/08/30 15:07:03 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:07:03 [14] Resources fetched - 1 - 
2025/08/30 15:07:04 [14] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:07:04 [14] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:07:04 [14] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:07:04 [14] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:07:05 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:07:05 Memory Stage: resource_context_loaded - Alloc: 1438 KB - Total: 148311 KB - Sys: 44305 KB - GC: 27 - Objects: 9296 - Duration: 1.915925542s - 
2025/08/30 15:07:05 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:07:05 Memory Stage: human_evaluation_initialized - Alloc: 2548 KB - Total: 149888 KB - Sys: 44305 KB - GC: 28 - Objects: 7879 - Duration: 6.913875ms - 
2025/08/30 15:07:06 Memory Stage: sync_maps_initialized - Alloc: 2618 KB - Total: 151924 KB - Sys: 44305 KB - GC: 29 - Objects: 17059 - Duration: 730.222541ms - 
2025/08/30 15:07:06 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:07:06 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:07:06 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:07:06 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:07:06 Memory Stage: stage1_start - Alloc: 2669 KB - Total: 151976 KB - Sys: 44305 KB - GC: 29 - Objects: 17818 - Duration: 4.026834ms - 
2025/08/30 15:07:06 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:07:06 Memory Stage: stage1_completed - Alloc: 2695 KB - Total: 153613 KB - Sys: 44305 KB - GC: 30 - Objects: 8976 - Duration: 7.639666ms - 
2025/08/30 15:07:06 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:07:06 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:07:11 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:07:11 Memory Stage: stage2_start - Alloc: 982 KB - Total: 155218 KB - Sys: 44305 KB - GC: 32 - Objects: 6829 - Duration: 5.130323542s - 
2025/08/30 15:07:11 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:08:22 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 8209 KB - GC: 0 - Objects: 2497 - Duration: 49.333µs - 
2025/08/30 15:08:22 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:08:22 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 8209 KB - GC: 0 - Objects: 2577 - Duration: 833.167µs - 
2025/08/30 15:08:22 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:08:29 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:08:31 Index iac_git_commits exists - 
2025/08/30 15:08:31 Index cfstack_templates exists - 
2025/08/30 15:08:32 Index arm_templates exists - 
2025/08/30 15:08:32 Index terraform_resources exists - 
2025/08/30 15:08:33 Index tf_commits exists - 
2025/08/30 15:08:34 Index tf_variables exists - 
2025/08/30 15:08:38 Index resource_context exists - 
2025/08/30 15:08:39 Index text_lookup exists - 
2025/08/30 15:08:46 Index ai_resources exists - 
2025/08/30 15:08:47 Index idp_events exists - 
2025/08/30 15:08:47 Index idp_users exists - 
2025/08/30 15:08:48 Index idp_apps exists - 
2025/08/30 15:08:49 Index idp_groups exists - 
2025/08/30 15:08:49 Index cloud_incidents exists - 
2025/08/30 15:08:50 Index jira_issues exists - 
2025/08/30 15:08:50 Index jira_data exists - 
2025/08/30 15:08:51 Index jira_resources exists - 
2025/08/30 15:08:51 Index precize_creations exists - 
2025/08/30 15:08:52 Index external_cloud_resources exists - 
2025/08/30 15:08:52 Memory Stage: elastic_connected - Alloc: 2063 KB - Total: 2228 KB - Sys: 9041 KB - GC: 1 - Objects: 3874 - Duration: 29.519253792s - 
2025/08/30 15:08:52 Memory Stage: services_initialized - Alloc: 2066 KB - Total: 2231 KB - Sys: 9041 KB - GC: 1 - Objects: 3919 - Duration: 412.333µs - 
2025/08/30 15:08:52 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:08:52 Memory Stage: before_processing - Alloc: 1884 KB - Total: 3485 KB - Sys: 13713 KB - GC: 2 - Objects: 2571 - Duration: 4.41325ms - 
2025/08/30 15:08:52 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:08:52 Memory Stage: service_id_resolved - Alloc: 1859 KB - Total: 4738 KB - Sys: 13969 KB - GC: 3 - Objects: 2562 - Duration: 2.113ms - 
2025/08/30 15:08:52 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:08:52 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:08:56 Memory Stage: tenant_data_loaded - Alloc: 2080 KB - Total: 4960 KB - Sys: 13969 KB - GC: 3 - Objects: 4203 - Duration: 3.997987583s - 
2025/08/30 15:08:56 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:08:57 Previous collected at - 1756463109875 - 
2025/08/30 15:09:05 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:09:05 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:09:08 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:09:14 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:09:22 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:09:23 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:09:24 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:09:25 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:09:25 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:09:25 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:09:26 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:09:26 Memory Stage: context_initialized - Alloc: 2255 KB - Total: 10642 KB - Sys: 14225 KB - GC: 6 - Objects: 13474 - Duration: 29.983576042s - 
2025/08/30 15:09:26 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:09:31 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:09:31 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:09:37 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:09:41 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:09:41 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Sai Ashish] - 
2025/08/30 15:09:42 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/08/30 15:09:42 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:09:42 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:09:45 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:09:45 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:09:45 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:09:45 Memory Stage: jira_context_loaded - Alloc: 2005 KB - Total: 142828 KB - Sys: 48529 KB - GC: 25 - Objects: 8255 - Duration: 19.5106785s - 
2025/08/30 15:09:45 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:09:45 [42] Resources fetched - 1 - 
2025/08/30 15:09:46 [42] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:09:46 [42] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:09:46 [42] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:09:46 [42] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:09:47 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:09:47 Memory Stage: resource_context_loaded - Alloc: 1974 KB - Total: 147122 KB - Sys: 48529 KB - GC: 27 - Objects: 12497 - Duration: 1.706699333s - 
2025/08/30 15:09:47 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:09:47 Memory Stage: human_evaluation_initialized - Alloc: 2624 KB - Total: 148705 KB - Sys: 48529 KB - GC: 28 - Objects: 7933 - Duration: 4.623334ms - 
2025/08/30 15:09:47 Memory Stage: sync_maps_initialized - Alloc: 2639 KB - Total: 150709 KB - Sys: 48529 KB - GC: 29 - Objects: 17082 - Duration: 472.691125ms - 
2025/08/30 15:09:47 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:09:47 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:09:47 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:09:47 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:09:47 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:09:47 Memory Stage: stage1_start - Alloc: 2691 KB - Total: 150761 KB - Sys: 48529 KB - GC: 29 - Objects: 17846 - Duration: 2.274208ms - 
2025/08/30 15:09:47 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:09:47 Memory Stage: stage1_completed - Alloc: 2704 KB - Total: 152406 KB - Sys: 48785 KB - GC: 30 - Objects: 8985 - Duration: 4.199708ms - 
2025/08/30 15:09:47 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:09:47 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:09:53 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:09:53 Memory Stage: stage2_start - Alloc: 988 KB - Total: 154008 KB - Sys: 48785 KB - GC: 32 - Objects: 6812 - Duration: 5.952644792s - 
2025/08/30 15:09:53 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:12:16 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 8465 KB - GC: 0 - Objects: 2496 - Duration: 60.167µs - 
2025/08/30 15:12:16 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:12:16 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 8465 KB - GC: 0 - Objects: 2576 - Duration: 859.875µs - 
2025/08/30 15:12:16 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:12:19 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:12:20 Index iac_git_commits exists - 
2025/08/30 15:12:21 Index cfstack_templates exists - 
2025/08/30 15:12:22 Index arm_templates exists - 
2025/08/30 15:12:22 Index terraform_resources exists - 
2025/08/30 15:12:23 Index tf_commits exists - 
2025/08/30 15:12:24 Index tf_variables exists - 
2025/08/30 15:12:25 Index resource_context exists - 
2025/08/30 15:12:26 Index text_lookup exists - 
2025/08/30 15:12:27 Index ai_resources exists - 
2025/08/30 15:12:27 Index idp_events exists - 
2025/08/30 15:12:28 Index idp_users exists - 
2025/08/30 15:12:29 Index idp_apps exists - 
2025/08/30 15:12:30 Index idp_groups exists - 
2025/08/30 15:12:31 Index cloud_incidents exists - 
2025/08/30 15:12:32 Index jira_issues exists - 
2025/08/30 15:12:32 Index jira_data exists - 
2025/08/30 15:12:33 Index jira_resources exists - 
2025/08/30 15:12:33 Index precize_creations exists - 
2025/08/30 15:12:33 Index external_cloud_resources exists - 
2025/08/30 15:12:33 Memory Stage: elastic_connected - Alloc: 2098 KB - Total: 2263 KB - Sys: 13393 KB - GC: 1 - Objects: 3865 - Duration: 17.612697875s - 
2025/08/30 15:12:33 Memory Stage: services_initialized - Alloc: 2101 KB - Total: 2265 KB - Sys: 13393 KB - GC: 1 - Objects: 3909 - Duration: 520.333µs - 
2025/08/30 15:12:33 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:12:33 Memory Stage: before_processing - Alloc: 1879 KB - Total: 3509 KB - Sys: 13457 KB - GC: 2 - Objects: 2557 - Duration: 4.32925ms - 
2025/08/30 15:12:33 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:12:33 Memory Stage: service_id_resolved - Alloc: 1849 KB - Total: 4757 KB - Sys: 13713 KB - GC: 3 - Objects: 2558 - Duration: 2.51225ms - 
2025/08/30 15:12:33 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:12:33 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:12:37 Memory Stage: tenant_data_loaded - Alloc: 2072 KB - Total: 4980 KB - Sys: 13713 KB - GC: 3 - Objects: 4204 - Duration: 3.448151375s - 
2025/08/30 15:12:37 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:12:37 Previous collected at - 1756463109875 - 
2025/08/30 15:12:38 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:12:38 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:12:38 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:12:39 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:12:39 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:12:39 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:12:39 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:12:40 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:12:40 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:12:40 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:12:40 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:12:40 Memory Stage: context_initialized - Alloc: 2609 KB - Total: 10739 KB - Sys: 13969 KB - GC: 6 - Objects: 15151 - Duration: 3.537135625s - 
2025/08/30 15:12:40 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:12:43 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:12:43 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:12:48 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:12:51 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:12:51 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Sowmya Test] - 
2025/08/30 15:12:52 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/08/30 15:12:52 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:12:52 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:12:55 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:12:55 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:12:55 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:12:55 Memory Stage: jira_context_loaded - Alloc: 15228 KB - Total: 141474 KB - Sys: 43729 KB - GC: 24 - Objects: 125771 - Duration: 14.732196458s - 
2025/08/30 15:12:55 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:12:55 [4] Resources fetched - 1 - 
2025/08/30 15:12:55 [4] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:12:56 [4] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:12:56 [4] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:12:56 [4] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:12:57 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:12:57 Memory Stage: resource_context_loaded - Alloc: 19293 KB - Total: 145540 KB - Sys: 43729 KB - GC: 24 - Objects: 158364 - Duration: 1.6035995s - 
2025/08/30 15:12:57 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:12:57 Memory Stage: human_evaluation_initialized - Alloc: 2407 KB - Total: 147120 KB - Sys: 43985 KB - GC: 26 - Objects: 7558 - Duration: 9.709167ms - 
2025/08/30 15:12:57 Memory Stage: sync_maps_initialized - Alloc: 2290 KB - Total: 149156 KB - Sys: 43985 KB - GC: 27 - Objects: 14089 - Duration: 233.4085ms - 
2025/08/30 15:12:57 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:12:57 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:12:57 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:12:57 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:12:57 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:12:57 Memory Stage: stage1_start - Alloc: 2341 KB - Total: 149208 KB - Sys: 43985 KB - GC: 27 - Objects: 14851 - Duration: 3.17875ms - 
2025/08/30 15:12:57 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:12:57 Memory Stage: stage1_completed - Alloc: 2700 KB - Total: 150850 KB - Sys: 43985 KB - GC: 28 - Objects: 9008 - Duration: 6.414917ms - 
2025/08/30 15:12:57 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:12:57 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:13:02 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:13:02 Memory Stage: stage2_start - Alloc: 983 KB - Total: 152459 KB - Sys: 43985 KB - GC: 30 - Objects: 6826 - Duration: 5.139804166s - 
2025/08/30 15:13:02 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:17:58 Memory Stage: startup - Alloc: 737 KB - Total: 737 KB - Sys: 6803 KB - GC: 0 - Objects: 2497 - Duration: 98.458µs - 
2025/08/30 15:17:58 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:17:58 Memory Stage: config_loaded - Alloc: 744 KB - Total: 744 KB - Sys: 6803 KB - GC: 0 - Objects: 2577 - Duration: 836.958µs - 
2025/08/30 15:17:58 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:17:59 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:17:59 Index iac_git_commits exists - 
2025/08/30 15:17:59 Index cfstack_templates exists - 
2025/08/30 15:17:59 Index arm_templates exists - 
2025/08/30 15:18:00 Index terraform_resources exists - 
2025/08/30 15:18:00 Index tf_commits exists - 
2025/08/30 15:18:00 Index tf_variables exists - 
2025/08/30 15:18:00 Index resource_context exists - 
2025/08/30 15:18:00 Index text_lookup exists - 
2025/08/30 15:18:00 Index ai_resources exists - 
2025/08/30 15:18:00 Index idp_events exists - 
2025/08/30 15:18:01 Index idp_users exists - 
2025/08/30 15:18:01 Index idp_apps exists - 
2025/08/30 15:18:01 Index idp_groups exists - 
2025/08/30 15:18:01 Index cloud_incidents exists - 
2025/08/30 15:18:01 Index jira_issues exists - 
2025/08/30 15:18:01 Index jira_data exists - 
2025/08/30 15:18:02 Index jira_resources exists - 
2025/08/30 15:18:02 Index precize_creations exists - 
2025/08/30 15:18:02 Index external_cloud_resources exists - 
2025/08/30 15:18:02 Memory Stage: elastic_connected - Alloc: 2087 KB - Total: 2251 KB - Sys: 13137 KB - GC: 1 - Objects: 3841 - Duration: 3.3857635s - 
2025/08/30 15:18:02 Memory Stage: services_initialized - Alloc: 2090 KB - Total: 2254 KB - Sys: 13137 KB - GC: 1 - Objects: 3887 - Duration: 595.917µs - 
2025/08/30 15:18:02 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:18:02 Memory Stage: before_processing - Alloc: 1872 KB - Total: 3495 KB - Sys: 13201 KB - GC: 2 - Objects: 2551 - Duration: 6.233375ms - 
2025/08/30 15:18:02 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:18:02 Memory Stage: service_id_resolved - Alloc: 1842 KB - Total: 4735 KB - Sys: 13201 KB - GC: 3 - Objects: 2539 - Duration: 2.747958ms - 
2025/08/30 15:18:02 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:18:02 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:18:05 Memory Stage: tenant_data_loaded - Alloc: 2062 KB - Total: 4955 KB - Sys: 13201 KB - GC: 3 - Objects: 4174 - Duration: 3.337625s - 
2025/08/30 15:18:05 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:18:05 Previous collected at - 1756463109875 - 
2025/08/30 15:18:06 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:18:06 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:18:07 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:18:07 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:18:08 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:18:09 Memory Stage: context_initialized - Alloc: 2443 KB - Total: 10688 KB - Sys: 13969 KB - GC: 6 - Objects: 13838 - Duration: 3.481804917s - 
2025/08/30 15:18:09 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:18:11 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:18:11 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:18:16 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:18:21 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:18:21 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Random Guy] - 
2025/08/30 15:18:21 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:undeliverable] - 
2025/08/30 15:18:21 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:18:21 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:18:25 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:18:25 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:18:25 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:18:25 Memory Stage: jira_context_loaded - Alloc: 14186 KB - Total: 144779 KB - Sys: 44177 KB - GC: 24 - Objects: 120876 - Duration: 16.327526292s - 
2025/08/30 15:18:25 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:18:25 [8] Resources fetched - 1 - 
2025/08/30 15:18:26 [8] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:18:26 [8] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:18:26 [8] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:18:26 [8] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:18:27 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:18:27 Memory Stage: resource_context_loaded - Alloc: 18334 KB - Total: 148927 KB - Sys: 44433 KB - GC: 24 - Objects: 153618 - Duration: 1.859502125s - 
2025/08/30 15:18:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:18:27 Memory Stage: human_evaluation_initialized - Alloc: 2880 KB - Total: 150496 KB - Sys: 44433 KB - GC: 25 - Objects: 7983 - Duration: 7.387625ms - 
2025/08/30 15:18:27 Memory Stage: sync_maps_initialized - Alloc: 2639 KB - Total: 152533 KB - Sys: 44433 KB - GC: 26 - Objects: 17095 - Duration: 438.825541ms - 
2025/08/30 15:18:27 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:18:27 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:18:27 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:18:27 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:18:27 Memory Stage: stage1_start - Alloc: 2690 KB - Total: 152585 KB - Sys: 44433 KB - GC: 26 - Objects: 17859 - Duration: 4.112ms - 
2025/08/30 15:18:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:18:27 Memory Stage: stage1_completed - Alloc: 2694 KB - Total: 154220 KB - Sys: 44433 KB - GC: 27 - Objects: 8961 - Duration: 7.656625ms - 
2025/08/30 15:18:27 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:18:27 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:18:32 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:18:32 Memory Stage: stage2_start - Alloc: 983 KB - Total: 155823 KB - Sys: 44433 KB - GC: 29 - Objects: 6826 - Duration: 5.120281042s - 
2025/08/30 15:18:32 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:41:47 Memory Stage: startup - Alloc: 736 KB - Total: 736 KB - Sys: 8465 KB - GC: 0 - Objects: 2495 - Duration: 50.667µs - 
2025/08/30 15:41:47 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:41:47 Memory Stage: config_loaded - Alloc: 743 KB - Total: 743 KB - Sys: 8465 KB - GC: 0 - Objects: 2575 - Duration: 777.208µs - 
2025/08/30 15:41:47 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:41:49 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:41:49 Index iac_git_commits exists - 
2025/08/30 15:41:50 Index cfstack_templates exists - 
2025/08/30 15:41:51 Index arm_templates exists - 
2025/08/30 15:41:52 Index terraform_resources exists - 
2025/08/30 15:41:52 Index tf_commits exists - 
2025/08/30 15:41:52 Index tf_variables exists - 
2025/08/30 15:41:53 Index resource_context exists - 
2025/08/30 15:41:53 Index text_lookup exists - 
2025/08/30 15:41:53 Index ai_resources exists - 
2025/08/30 15:41:53 Index idp_events exists - 
2025/08/30 15:41:53 Index idp_users exists - 
2025/08/30 15:41:54 Index idp_apps exists - 
2025/08/30 15:41:54 Index idp_groups exists - 
2025/08/30 15:41:54 Index cloud_incidents exists - 
2025/08/30 15:41:54 Index jira_issues exists - 
2025/08/30 15:41:54 Index jira_data exists - 
2025/08/30 15:41:55 Index jira_resources exists - 
2025/08/30 15:41:55 Index precize_creations exists - 
2025/08/30 15:41:55 Index external_cloud_resources exists - 
2025/08/30 15:41:55 Memory Stage: elastic_connected - Alloc: 2054 KB - Total: 2219 KB - Sys: 13137 KB - GC: 1 - Objects: 3825 - Duration: 8.693077083s - 
2025/08/30 15:41:55 Memory Stage: services_initialized - Alloc: 2057 KB - Total: 2222 KB - Sys: 13137 KB - GC: 1 - Objects: 3871 - Duration: 291.959µs - 
2025/08/30 15:41:55 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:41:55 Memory Stage: before_processing - Alloc: 1869 KB - Total: 3456 KB - Sys: 13201 KB - GC: 2 - Objects: 2513 - Duration: 6.049916ms - 
2025/08/30 15:41:55 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:41:55 Memory Stage: service_id_resolved - Alloc: 1833 KB - Total: 4684 KB - Sys: 13201 KB - GC: 3 - Objects: 2496 - Duration: 2.241417ms - 
2025/08/30 15:41:55 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:41:55 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:42:01 Memory Stage: tenant_data_loaded - Alloc: 2055 KB - Total: 4905 KB - Sys: 13201 KB - GC: 3 - Objects: 4137 - Duration: 5.585771875s - 
2025/08/30 15:42:01 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:42:01 Previous collected at - 1756463109875 - 
2025/08/30 15:42:02 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:42:02 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:42:03 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:42:03 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:42:05 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:42:05 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:42:05 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:42:05 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:42:06 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:42:06 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:42:06 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:42:06 Memory Stage: context_initialized - Alloc: 2727 KB - Total: 10723 KB - Sys: 14481 KB - GC: 6 - Objects: 15860 - Duration: 5.114768625s - 
2025/08/30 15:42:06 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:42:08 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:42:08 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:42:15 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:42:18 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/08/30 15:42:18 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Sowmya Gowda] - 
2025/08/30 15:42:19 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:undeliverable] - 
2025/08/30 15:42:19 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:42:19 Email Status Response - map[<EMAIL>:invalid] - 
2025/08/30 15:42:22 [0R8Da4gBoELr5xpoQ6Y3] Gathered cloud users - 
2025/08/30 15:42:22 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/08/30 15:42:22 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/08/30 15:42:22 Memory Stage: jira_context_loaded - Alloc: 17143 KB - Total: 138669 KB - Sys: 44177 KB - GC: 23 - Objects: 143044 - Duration: 16.229663s - 
2025/08/30 15:42:22 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/08/30 15:42:22 [8] Resources fetched - 1 - 
2025/08/30 15:42:23 [8] Email Status Request - map[<EMAIL>:Sowmyachandrappa] - 
2025/08/30 15:42:23 [8] Email Status Response - map[<EMAIL>:deliverable] - 
2025/08/30 15:42:23 [8] Email Status Request - map[<EMAIL>:Vishalakshi] - 
2025/08/30 15:42:23 [8] Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:42:24 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/08/30 15:42:24 Memory Stage: resource_context_loaded - Alloc: 2006 KB - Total: 142748 KB - Sys: 44433 KB - GC: 25 - Objects: 12788 - Duration: 1.835865833s - 
2025/08/30 15:42:24 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_all_contexts_loaded_*************.prof - 
2025/08/30 15:42:24 Memory Stage: human_evaluation_initialized - Alloc: 2647 KB - Total: 144315 KB - Sys: 44433 KB - GC: 26 - Objects: 7989 - Duration: 6.493584ms - 
2025/08/30 15:42:24 Memory Stage: sync_maps_initialized - Alloc: 2593 KB - Total: 146319 KB - Sys: 44433 KB - GC: 27 - Objects: 17048 - Duration: 270.925458ms - 
2025/08/30 15:42:24 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/08/30 15:42:24 n:preprodprecizeorgonboarding <iam role> - UserContext:  Name: PreprodPrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Abhishek G  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Sowmya Precize  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: true  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Abhay Anoop  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Sai Ashish  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Sowmya Test  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Prasad B  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Random Guy  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Sowmyachandrappa  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Muskaan Kumari  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Prasad Batta  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 n:precize_a99cf <iam user> - UserContext:  Name: Precize_a99cf <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 n:precizeorgonboarding <iam role> - UserContext:  Name: PrecizeOrgOnboarding <IAM Role>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 n:precize_9d465 <iam user> - UserContext:  Name: Precize_9d465 <IAM User>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 n:************ <aws account> - UserContext:  Name: ************ <AWS Account>  Email:   Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 1  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Abhay Reetha Anoop  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Vishwas Manral  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Megha Gowda  Email: <EMAIL>  Active: true  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Aniket Dinda  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts:  - 
2025/08/30 15:42:24 <EMAIL> - UserContext:  Name: Vishalakshi  Email: <EMAIL>  Active: false  IsUser: true  IsUserEvaluated: true  IsInvalid: false  IsSddl: false  SkipUser: false  ActiveAccounts: ************: 2  - 
2025/08/30 15:42:24 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 1000 - ************* - 
2025/08/30 15:42:24 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 1000 - ************* - 
2025/08/30 15:42:24 Memory Stage: stage1_start - Alloc: 2644 KB - Total: 146371 KB - Sys: 44433 KB - GC: 27 - Objects: 17807 - Duration: 3.589333ms - 
2025/08/30 15:42:24 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_start_*************.prof - 
2025/08/30 15:42:24 Memory Stage: stage1_completed - Alloc: 2689 KB - Total: 147995 KB - Sys: 44433 KB - GC: 28 - Objects: 9026 - Duration: 8.127334ms - 
2025/08/30 15:42:24 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage1_completed_*************.prof - 
2025/08/30 15:42:24 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 1000 - ************* - 
2025/08/30 15:42:29 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 1000 - ************* - 
2025/08/30 15:42:29 Memory Stage: stage2_start - Alloc: 989 KB - Total: 149604 KB - Sys: 44433 KB - GC: 30 - Objects: 6865 - Duration: 5.113263083s - 
2025/08/30 15:42:29 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_stage2_start_*************.prof - 
2025/08/30 15:44:35 Memory Stage: startup - Alloc: 735 KB - Total: 735 KB - Sys: 8209 KB - GC: 0 - Objects: 2490 - Duration: 71.459µs - 
2025/08/30 15:44:35 Application config could not be read. Starting with defaults - application.yml - 
2025/08/30 15:44:35 Memory Stage: config_loaded - Alloc: 742 KB - Total: 742 KB - Sys: 8209 KB - GC: 0 - Objects: 2570 - Duration: 1.075ms - 
2025/08/30 15:44:35 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_config_loaded_*************.prof - 
2025/08/30 15:44:35 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/08/30 15:44:35 Index iac_git_commits exists - 
2025/08/30 15:44:35 Index cfstack_templates exists - 
2025/08/30 15:44:35 Index arm_templates exists - 
2025/08/30 15:44:35 Index terraform_resources exists - 
2025/08/30 15:44:36 Index tf_commits exists - 
2025/08/30 15:44:36 Index tf_variables exists - 
2025/08/30 15:44:36 Index resource_context exists - 
2025/08/30 15:44:36 Index text_lookup exists - 
2025/08/30 15:44:36 Index ai_resources exists - 
2025/08/30 15:44:37 Index idp_events exists - 
2025/08/30 15:44:37 Index idp_users exists - 
2025/08/30 15:44:37 Index idp_apps exists - 
2025/08/30 15:44:38 Index idp_groups exists - 
2025/08/30 15:44:38 Index cloud_incidents exists - 
2025/08/30 15:44:38 Index jira_issues exists - 
2025/08/30 15:44:38 Index jira_data exists - 
2025/08/30 15:44:38 Index jira_resources exists - 
2025/08/30 15:44:38 Index precize_creations exists - 
2025/08/30 15:44:38 Index external_cloud_resources exists - 
2025/08/30 15:44:38 Memory Stage: elastic_connected - Alloc: 2095 KB - Total: 2259 KB - Sys: 13137 KB - GC: 1 - Objects: 3851 - Duration: 3.85719375s - 
2025/08/30 15:44:38 Memory Stage: services_initialized - Alloc: 2098 KB - Total: 2262 KB - Sys: 13137 KB - GC: 1 - Objects: 3895 - Duration: 377.75µs - 
2025/08/30 15:44:38 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_services_initialized_*************.prof - 
2025/08/30 15:44:38 Memory Stage: before_processing - Alloc: 1901 KB - Total: 3529 KB - Sys: 13713 KB - GC: 2 - Objects: 2597 - Duration: 3.56ms - 
2025/08/30 15:44:38 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_before_processing_*************.prof - 
2025/08/30 15:44:38 Memory Stage: service_id_resolved - Alloc: 1873 KB - Total: 4792 KB - Sys: 13969 KB - GC: 3 - Objects: 2599 - Duration: 2.212375ms - 
2025/08/30 15:44:38 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 1000 - ************* - 
2025/08/30 15:44:38 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/08/30 15:44:43 Memory Stage: tenant_data_loaded - Alloc: 2094 KB - Total: 5014 KB - Sys: 13969 KB - GC: 3 - Objects: 4241 - Duration: 4.155580333s - 
2025/08/30 15:44:43 Heap profile saved: /Users/<USER>/Desktop/Aniket-precize/precize-provider1.nosync/precize-provider/enhancer/profiles/0R8Da4gBoELr5xpoQ6Y3/heap_1000_tenant_data_loaded_*************.prof - 
2025/08/30 15:44:43 Previous collected at - 1756463109875 - 
2025/08/30 15:44:43 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context Precize Precize QA QA Activity Dev Prod Weather Test Wireguard Vercel Redis] - 
2025/08/30 15:44:43 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [Dev Production QA QA-Automation Precize QAtestchanged QA-API-Automation QA-rangers Ind team Test Precize-QA-Auto US Manual Testing QAtest Dev Group Devops-India Precize-Amulya Amulya Random Precize-Qa Platform Infradevcore] - 
2025/08/30 15:44:44 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:44:46 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/08/30 15:44:46 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/08/30 15:44:47 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/08/30 15:44:47 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/08/30 15:44:47 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/08/30 15:44:47 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:44:47 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/08/30 15:44:49 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:dVQztJYBprrVv9ZWC7Vh key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/08/30 15:44:49 Memory Stage: context_initialized - Alloc: 2517 KB - Total: 10701 KB - Sys: 13969 KB - GC: 6 - Objects: 14960 - Duration: 6.488047875s - 
2025/08/30 15:44:49 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/08/30 15:44:52 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/08/30 15:44:53 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/08/30 15:45:04 Email Status Request - map[<EMAIL>:Abhay <NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/08/30 15:45:08 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/08/30 15:45:08 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Prasad Batta] - 
2025/08/30 15:45:08 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/08/30 15:45:08 Email Status Request - map[<EMAIL>:Megha Gowda] - 
2025/08/30 15:45:08 Email Status Response - map[<EMAIL>:invalid] - 
