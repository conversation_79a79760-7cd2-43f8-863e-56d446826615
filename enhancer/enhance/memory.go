package enhance

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"runtime/pprof"
	"strings"
	"time"

	"github.com/precize/logger"
)

type MemoryProfiler struct {
	tenantID        string
	lastCollectedAt string
	serviceID       string
	startTime       time.Time
	stages          []MemoryStage
}

type MemoryStage struct {
	name          string
	alloc         uint64
	totalAlloc    uint64
	sys           uint64
	numGC         uint32
	timestamp     time.Time
	duration      time.Duration
	heapObjects   uint64
	stackInUse    uint64
	gcCPUFraction float64
}

func NewMemoryProfiler(tenantID, lastCollectedAt, serviceID string) *MemoryProfiler {
	return &MemoryProfiler{
		tenantID:        tenantID,
		lastCollectedAt: lastCollectedAt,
		serviceID:       serviceID,
		startTime:       time.Now(),
		stages:          make([]MemoryStage, 0, 20),
	}
}

func (mp *MemoryProfiler) RecordStage(stageName string) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	now := time.Now()
	var duration time.Duration
	if len(mp.stages) > 0 {
		duration = now.Sub(mp.stages[len(mp.stages)-1].timestamp)
	} else {
		duration = now.Sub(mp.startTime)
	}

	stage := MemoryStage{
		name:          stageName,
		alloc:         m.Alloc,
		totalAlloc:    m.TotalAlloc,
		sys:           m.Sys,
		numGC:         m.NumGC,
		timestamp:     now,
		duration:      duration,
		heapObjects:   m.HeapObjects,
		stackInUse:    m.StackInuse,
		gcCPUFraction: m.GCCPUFraction,
	}

	mp.stages = append(mp.stages, stage)

	// Log real-time memory stats
	logger.Print(logger.INFO, fmt.Sprintf("Memory Stage: %s", stageName),
		fmt.Sprintf("Alloc: %d KB", m.Alloc/1024),
		fmt.Sprintf("Total: %d KB", m.TotalAlloc/1024),
		fmt.Sprintf("Sys: %d KB", m.Sys/1024),
		fmt.Sprintf("GC: %d", m.NumGC),
		fmt.Sprintf("Objects: %d", m.HeapObjects),
		fmt.Sprintf("Duration: %v", duration))
}

func (mp *MemoryProfiler) SaveHeapProfile(stageName string) error {
	// Get binary directory (not the cwd)
	exePath, err := os.Executable()
	if err != nil {
		return fmt.Errorf("could not get executable path: %v", err)
	}
	baseDir := filepath.Dir(exePath)

	// Create directory structure: {binaryDir}/profiles/{tenantID}/
	profileDir := filepath.Join(baseDir, "profiles", mp.tenantID)
	if err := os.MkdirAll(profileDir, 0755); err != nil {
		return fmt.Errorf("could not create profile directory %s: %v", profileDir, err)
	}

	// Create filename with path
	filename := filepath.Join(
		profileDir,
		fmt.Sprintf("heap_%s_%s_%s.prof", mp.serviceID, stageName, mp.lastCollectedAt),
	)

	f, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("could not create heap profile %s: %v", filename, err)
	}
	defer f.Close()

	// Force GC for accurate profile
	runtime.GC()
	if err := pprof.WriteHeapProfile(f); err != nil {
		return fmt.Errorf("could not write heap profile: %v", err)
	}

	logger.Print(logger.INFO, fmt.Sprintf("Heap profile saved: %s", filename))
	return nil
}

func (mp *MemoryProfiler) GenerateReport() {
	// Create directory structure: profiles/{tenantID}/
	profileDir := fmt.Sprintf("profiles/%s", mp.tenantID)
	if err := os.MkdirAll(profileDir, 0755); err != nil {
		logger.Print(logger.ERROR, "Could not create profile directory", err)
		return
	}

	reportFile := fmt.Sprintf("%s/memory_report_%s_%s.txt",
		profileDir, mp.serviceID, mp.lastCollectedAt)

	f, err := os.Create(reportFile)
	if err != nil {
		logger.Print(logger.ERROR, "Could not create memory report", err)
		return
	}
	defer f.Close()

	totalDuration := time.Since(mp.startTime)

	fmt.Fprintf(f, "=== Memory Profiling Report ===\n")
	fmt.Fprintf(f, "Tenant ID: %s\n", mp.tenantID)
	fmt.Fprintf(f, "Service ID: %s\n", mp.serviceID)
	fmt.Fprintf(f, "Last Collected At: %s\n", mp.lastCollectedAt)
	fmt.Fprintf(f, "Total Duration: %v\n\n", totalDuration)

	fmt.Fprintf(f, "Stage-wise Memory Usage:\n")
	fmt.Fprintf(f, "%-25s %-12s %-12s %-12s %-8s %-12s %-10s %-8s\n",
		"Stage", "Alloc(KB)", "Total(KB)", "Sys(KB)", "GC", "Objects", "Duration", "GC CPU%")
	fmt.Fprintf(f, "%s\n", strings.Repeat("-", 120))

	for i, stage := range mp.stages {
		var allocDiff int64
		if i > 0 {
			allocDiff = int64(stage.totalAlloc) - int64(mp.stages[i-1].totalAlloc)
		}

		fmt.Fprintf(f, "%-25s %-12d %-12d %-12d %-8d %-12d %-10v %-.2f%%\n",
			stage.name,
			stage.alloc/1024,
			stage.totalAlloc/1024,
			stage.sys/1024,
			stage.numGC,
			stage.heapObjects,
			stage.duration,
			stage.gcCPUFraction*100)

		if allocDiff > 0 {
			fmt.Fprintf(f, "  → Allocated in this stage: %d KB\n", allocDiff/1024)
		}
	}

	// Find peak memory usage
	var peakStage MemoryStage
	for _, stage := range mp.stages {
		if stage.alloc > peakStage.alloc {
			peakStage = stage
		}
	}

	fmt.Fprintf(f, "\nPeak Memory Usage: %d KB at stage '%s'\n",
		peakStage.alloc/1024, peakStage.name)

	logger.Print(logger.INFO, fmt.Sprintf("Memory report generated: %s", reportFile))
}
