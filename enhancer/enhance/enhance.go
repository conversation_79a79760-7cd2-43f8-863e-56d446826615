package enhance

import (
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/context-evaluator/service"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/identity"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/enhancer/internal/sddl"
	"github.com/precize/enhancer/postprocess"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/resource"
	"github.com/precize/enhancer/source/code"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/enhancer/source/global"
	"github.com/precize/enhancer/source/issue"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const (
	MAX_THREADS = 10
)

func StartContextProcessing(tenantID, lastCollectedAt, serviceID string, debugMode bool, profiler *MemoryProfiler) {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("enhancer")
		}

		logger.LogEmailProcessor("", true)

		if profiler != nil {
			profiler.RecordStage("function_exit")
		}
	}()

	if len(serviceID) <= 0 {
		serviceID = common.GetServiceID(tenantID, lastCollectedAt)
		if len(serviceID) <= 0 {
			logger.Print(logger.INFO, "Invalid collectedAt at or tenantId", tenantID, lastCollectedAt)
			return
		}
	}

	if profiler != nil {
		profiler.RecordStage("service_id_resolved")
	}

	logger.Print(logger.INFO, "Processing context data for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	tenantData, err := tenant.GetTenantData(tenantID, false)
	if err != nil {
		return
	}

	if profiler != nil {
		profiler.RecordStage("tenant_data_loaded")
		profiler.SaveHeapProfile("tenant_data_loaded")
	}

	startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID, tenantData, debugMode, profiler)
}

func startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID string, tenantData tenant.TenantData, debugMode bool, profiler *MemoryProfiler) {

	var (
		previousCollectedAt = common.GetPreviousCollectedAt(tenantID, lastCollectedAt, serviceID)
		resourceContext     = rcontext.NewResourceContext(tenantID, lastCollectedAt, previousCollectedAt, serviceID, tenantData)
		currentTime         = time.Now().UTC()
		// All insertions for enhancer should happen here and must check for persistEnhancerData flag
		persistEnhancerData = !debugMode
	)

	resourceContext.EmailFormats, _ = emailutils.GetEmailFormatsForTenant(tenantID)
	resourceContext.SDDLStatus, _ = sddl.GetSDDLStatus(tenantID)
	resourceContext.TenantTfApproach, _ = common.FetchTenantTfApproach(tenantID)
	resourceContext.PrimaryDomains = emailutils.GetPrimaryDomains(tenantID)
	global.GetGlobalOrgContext(resourceContext)
	customer.GetCustomerDefinedContexts(resourceContext)
	context.GetAllOwnerExceptionsForTenant(resourceContext)
	context.GetAllOwnerInclusionsForTenant(resourceContext)
	context.GetAllTypoExceptionsForTenant(resourceContext)
	context.GetAllOwnerEmailNamesForTenant(resourceContext)
	context.GetAllEmailDerivationExceptionsForTenant(resourceContext)
	context.GetAllEmailDerivationInclusionsForTenant(resourceContext)
	context.GetAllChildToPrimaryEmailInclusionsForTenant(resourceContext)

	sensitivityExceptions := context.GetSensitivityExceptionsForTenant()
	for k, v := range sensitivityExceptions {
		resourceContext.SetSensitivityExceptions(k, v)
	}

	if profiler != nil {
		profiler.RecordStage("context_initialized")
	}

	identity.GetUsers(resourceContext)

	// if profiler != nil {
	// 	profiler.RecordStage("users_loaded")
	// 	profiler.SaveHeapProfile("users_loaded")
	// }

	// if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
	// 	resource.GetOktaContext(resourceContext)
	// 	if profiler != nil {
	// 		profiler.RecordStage("okta_context_loaded")
	// 	}
	// }

	if enabled, ok := resourceContext.GetEnabledService("jira"); ok && enabled {
		issue.GetJiraContext(resourceContext)
		if profiler != nil {
			profiler.RecordStage("jira_context_loaded")
		}
	}

	switch serviceID {

	case common.AWS_SERVICE_ID:
		// resource.GetAWSOrgContext(resourceContext)
		// resource.GetAWSUserAndRoleContext(resourceContext)
		// resource.GetOrgUnitContext(resourceContext)
		// resource.GetAccountContext(resourceContext)
		// resource.GetSecurityGroupRules(resourceContext)

	case common.AZURE_SERVICE_ID:
		resource.GetTenantContext(resourceContext)
		resource.GetApplicationContext(resourceContext)
		resource.GetRoleAssignments(resourceContext)
		resource.GetADUserContext(resourceContext)
		resource.GetManagementGroupContext(resourceContext)
		resource.GetSubscriptionContext(resourceContext)
		resource.GetResourceGroupContext(resourceContext)
		resource.GetNetworkSecurityGroupRules(resourceContext)

	case common.GCP_SERVICE_ID:
		resource.GetRoles(resourceContext)
		resource.GetGCPGroupContext(resourceContext)
		resource.GetGCPOrgContext(resourceContext)
		resource.GetServiceAccountKeyContext(resourceContext)
		resource.GetServiceAccountContext(resourceContext)
		resource.GetSAPolicyBindingContext(resourceContext)
		resource.GetFolderContext(resourceContext)
		resource.GetProjectContext(resourceContext)
		resource.GetFirewallRules(resourceContext)

	case common.OPENAI_SERVICE_ID:
		resource.GetOpenAIOrgContext(resourceContext)
		resource.GetOpenAIProjectContext(resourceContext)
	}

	// identity.GetIdentityAppsContext(resourceContext)
	// if profiler != nil {
	// 	profiler.RecordStage("identity_apps_context")
	// 	profiler.SaveHeapProfile("identity_apps_context")
	// }

	resource.GetResourceContext(resourceContext)
	if profiler != nil {
		profiler.RecordStage("resource_context_loaded")
		profiler.SaveHeapProfile("all_contexts_loaded")
	}

	nameList := make(map[string][]string)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource *rcontext.UserContext) bool {
		fmt.Println()
		return true
	})

	resourceContext.RangeUserResources(func(usrRscKey string, userResource *rcontext.UserContext) bool {
		identity.InitializeHumanOrNonHumanEvaluation(usrRscKey, userResource, resourceContext, nameList)
		return true
	})

	if profiler != nil {
		profiler.RecordStage("human_evaluation_initialized")
	}

	identity.EvaluateNamesForHumanOrNonHuman(nameList, resourceContext)

	var (
		uniqueOwners       sync.Map
		uniqueEnv          sync.Map
		uniqueApp          sync.Map
		uniqueSoftware     sync.Map
		uniqueDeployment   sync.Map
		uniqueCompliance   sync.Map
		uniqueSensitivity  sync.Map
		uniqueCostCenter   sync.Map
		uniqueTeam         sync.Map
		accountSensitivity sync.Map
		accountCompliance  sync.Map
		uniqueTTL          sync.Map
		uniqueUsrAgent     sync.Map
	)

	if profiler != nil {
		profiler.RecordStage("sync_maps_initialized")
	}

	logger.Print(logger.INFO, "User list for post processing", []string{tenantID}, lastCollectedAt)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource *rcontext.UserContext) bool {
		logger.Print(logger.INFO, usrRscKey, userResource.Print())
		return true
	})

	logger.Print(logger.INFO, "Starting post process", []string{tenantID}, serviceID, lastCollectedAt)

	var wg sync.WaitGroup
	semStage1 := make(chan struct{}, MAX_THREADS)

	logger.Print(logger.INFO, "Starting post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	if profiler != nil {
		profiler.RecordStage("stage1_start")
		profiler.SaveHeapProfile("stage1_start")
	}

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {
		semStage1 <- struct{}{}
		wg.Add(1)

		go func(docID string, rContextDoc *common.ResourceContextInsertDoc) {

			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", r, docID)
					email.SendPanicEmail("enhancer")
				}
				wg.Done()
				<-semStage1
			}()

			owners := context.PostProcessOwners(rContextDoc, resourceContext)
			uniqueOwners.Store(docID, owners)

			envContext := context.GetUniqueEnvContext(rContextDoc, resourceContext)
			uniqueEnv.Store(docID, envContext)

			appContext := context.GetUniqueAppContext(rContextDoc)
			uniqueApp.Store(docID, appContext)

			teamContext := context.GetUniqueTeamContext(rContextDoc)
			uniqueTeam.Store(docID, teamContext)

			softwareContext := context.GetUniqueSoftwareContext(rContextDoc)
			uniqueSoftware.Store(docID, softwareContext)

			deploymentContext := context.GetUniqueDeploymentContext(rContextDoc)
			uniqueDeployment.Store(docID, deploymentContext)

			complianceContext := context.GetUniqueComplianceContext(rContextDoc, &accountCompliance)
			uniqueCompliance.Store(docID, complianceContext)

			sensitivityContext := context.GetUniqueSensitivityContext(rContextDoc, &accountSensitivity)
			uniqueSensitivity.Store(docID, sensitivityContext)

			costCenterContext := context.GetUniqueCostCenterContext(rContextDoc)
			uniqueCostCenter.Store(docID, costCenterContext)

			ttlContext := context.GetUniqueTTLContext(rContextDoc)
			uniqueTTL.Store(docID, ttlContext)

			usrAgentContext := context.GetUniqueUserAgentContext(rContextDoc)
			uniqueUsrAgent.Store(docID, usrAgentContext)

			code.PostProcessCommitContext(resourceContext, rContextDoc, docID)

			ownersInterface, _ := uniqueOwners.Load(docID)
			if owners, ok := ownersInterface.([]string); ok {
				for _, owner := range owners {
					// Only increment for resources where owner is present
					context.IncrementParentChildOwnerCount(resourceContext, owner, rContextDoc.Account)
				}
			}

			// Insert in map even if owner is absent
			context.IncrementResourceTypeOwnerCount(resourceContext, *rContextDoc, ownersInterface.([]string))

			resourceContext.SetResourceContextInsertDoc(docID, *rContextDoc)
		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage1)

	if profiler != nil {
		profiler.RecordStage("stage1_completed")
		profiler.SaveHeapProfile("stage1_completed")
	}

	logger.Print(logger.INFO, "Completed post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {
		emailutils.UpdateUndeliverableValidEmails(resourceContext)
	}

	logger.Print(logger.INFO, "Starting post process stage 2", []string{tenantID}, serviceID, lastCollectedAt)

	if profiler != nil {
		profiler.RecordStage("stage2_start")
		profiler.SaveHeapProfile("stage2_start")
	}

	var (
		bulkResourceContextRequest, bulkCloudResourceRequest strings.Builder
		recordsCount                                         int
		maxRecords                                           = 10000
		identitiesMap                                        sync.Map
		bulkMutex                                            sync.Mutex
		identityCount                                        int32
		identityMapLock                                      sync.RWMutex
	)

	semStage2 := make(chan struct{}, 1)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {

		semStage2 <- struct{}{}
		wg.Add(1)
		go func(docID string, resourceContextDoc *common.ResourceContextInsertDoc) {

			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", r, docID)
					email.SendPanicEmail("enhancer")
				}

				wg.Done()
				<-semStage2
			}()

			var (
				enhancedUniqueOwners = make([]string, 0, 10)
				enhancedUniqueApps   = make([]string, 0, 5)
				enhancedUniqueTeams  = make([]string, 0, 5)
				relatedResources     []byte
				err                  error
				uniqueUsers          = make([]string, 0, 20)
			)

			if resourceContextDoc.SkipContext {

				owner := "Precize Support" + " <" + strings.ToLower("<EMAIL>") + ">"

				resourceContextDoc.ResourceOwnerTypes.DefinedOwners = append(
					resourceContextDoc.ResourceOwnerTypes.DefinedOwners,
					common.ResourceContextItem{
						Name:       owner,
						Type:       common.PRECIZE_DETECTED_USER_TYPE,
						Desc:       context.GetStaticDescriptionOfUserType(common.PRECIZE_DETECTED_USER_TYPE),
						IdentityId: "<EMAIL>",
					},
				)

				resourceContextDoc.LastCollectedAt = lastCollectedAt
				resourceContextDoc.UpdatedTime = elastic.DateTime(currentTime)
				resourceContextDoc.ID = docID

				resourceContext.SetResourceContextInsertDoc(docID, *resourceContextDoc)

				resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					return
				}

				bulkMutex.Lock()
				bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
				bulkResourceContextRequest.WriteString("\n")
				bulkResourceContextRequest.Write(resourceContextInsertDoc)
				bulkResourceContextRequest.WriteString("\n")
				bulkMutex.Unlock()

				recordsCount++
				return
			}

			// Get unique owners, apps and teams from sync.Map
			if ownersVal, ok := uniqueOwners.Load(docID); ok {
				enhancedUniqueOwners = append(enhancedUniqueOwners, ownersVal.([]string)...)
			}

			if appsVal, ok := uniqueApp.Load(docID); ok {
				enhancedUniqueApps = append(enhancedUniqueApps, appsVal.([]string)...)
			}

			if teamsVal, ok := uniqueTeam.Load(docID); ok {
				enhancedUniqueTeams = append(enhancedUniqueTeams, teamsVal.([]string)...)
			}

			context.PostProcessServiceIdentities(resourceContext, resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			postprocess.PostProcessRelatedResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			postprocess.PostProcessMaxRelatedOwner(docID, resourceContext, resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			postprocess.PostProcessSimilarResourceNames(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			postprocess.PostProcessSameAppResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueTeams)

			postprocess.PostProcessSameTagResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			postprocess.PostProcessResourceTypeOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners)

			postprocess.PostProcessSmallAccounts(resourceContext, resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			postprocess.PostProcessParentComplianceAndSensitivity(docID, resourceContextDoc, &uniqueSensitivity, &uniqueCompliance, &accountSensitivity, &accountCompliance)

			postprocess.PostProcessTeamsOfOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners, &enhancedUniqueTeams)

			// Remove excluded context from the resource - Doing it here because we have final list here
			var uniqueMap = map[string][]string{}

			// Load values from sync.Maps
			if envVal, ok := uniqueEnv.Load(docID); ok {
				uniqueMap[property.ENVIRONMENT_PROPERTY_NAME] = envVal.([]string)
			} else {
				uniqueMap[property.ENVIRONMENT_PROPERTY_NAME] = []string{}
			}

			if softwareVal, ok := uniqueSoftware.Load(docID); ok {
				uniqueMap[property.SOFTWARE_PROPERTY_NAME] = softwareVal.([]string)
			} else {
				uniqueMap[property.SOFTWARE_PROPERTY_NAME] = []string{}
			}

			if sensVal, ok := uniqueSensitivity.Load(docID); ok {
				uniqueMap[property.SENSITIVITY_PROPERTY_NAME] = sensVal.([]string)
			} else {
				uniqueMap[property.SENSITIVITY_PROPERTY_NAME] = []string{}
			}

			if compVal, ok := uniqueCompliance.Load(docID); ok {
				uniqueMap[property.COMPLIANCE_PROPERTY_NAME] = compVal.([]string)
			} else {
				uniqueMap[property.COMPLIANCE_PROPERTY_NAME] = []string{}
			}

			if depVal, ok := uniqueDeployment.Load(docID); ok {
				uniqueMap[property.DEPLOYMENT_PROPERTY_NAME] = depVal.([]string)
			} else {
				uniqueMap[property.DEPLOYMENT_PROPERTY_NAME] = []string{}
			}

			if ccVal, ok := uniqueCostCenter.Load(docID); ok {
				uniqueMap[property.COSTCENTER_PROPERTY_NAME] = ccVal.([]string)
			} else {
				uniqueMap[property.COSTCENTER_PROPERTY_NAME] = []string{}
			}

			if ttlVal, ok := uniqueTTL.Load(docID); ok {
				uniqueMap[property.TTL_PROPERTY_NAME] = ttlVal.([]string)
			} else {
				uniqueMap[property.TTL_PROPERTY_NAME] = []string{}
			}

			if uaVal, ok := uniqueUsrAgent.Load(docID); ok {
				uniqueMap[property.USER_AGENT_PROPERTY_NAME] = uaVal.([]string)
			} else {
				uniqueMap[property.USER_AGENT_PROPERTY_NAME] = []string{}
			}

			uniqueMap[property.OWNER_PROPERTY_NAME] = enhancedUniqueOwners
			uniqueMap[property.TEAM_PROPERTY_NAME] = enhancedUniqueTeams
			uniqueMap[property.APP_PROPERTY_NAME] = enhancedUniqueApps

			customer.RemoveCustomerEntityExcludeContextOfResource(resourceContext, resourceContextDoc, uniqueMap)

			if !resourceContextDoc.SkipContext && !resourceContextDoc.IsDefaultResource {
				service.DeriveContextLabels(resourceContextDoc)
			}

			// Store updated values back to sync.Maps
			enhancedUniqueOwners = uniqueMap[property.OWNER_PROPERTY_NAME]
			uniqueEnv.Store(docID, uniqueMap[property.ENVIRONMENT_PROPERTY_NAME])
			enhancedUniqueApps = uniqueMap[property.APP_PROPERTY_NAME]
			enhancedUniqueTeams = uniqueMap[property.TEAM_PROPERTY_NAME]
			uniqueSoftware.Store(docID, uniqueMap[property.SOFTWARE_PROPERTY_NAME])
			uniqueSensitivity.Store(docID, uniqueMap[property.SENSITIVITY_PROPERTY_NAME])
			uniqueCompliance.Store(docID, uniqueMap[property.COMPLIANCE_PROPERTY_NAME])
			uniqueDeployment.Store(docID, uniqueMap[property.DEPLOYMENT_PROPERTY_NAME])
			uniqueCostCenter.Store(docID, uniqueMap[property.COSTCENTER_PROPERTY_NAME])
			uniqueTTL.Store(docID, uniqueMap[property.TTL_PROPERTY_NAME])
			uniqueUsrAgent.Store(docID, uniqueMap[property.USER_AGENT_PROPERTY_NAME])

			// Set default values if empty
			if len(enhancedUniqueOwners) <= 0 {
				enhancedUniqueOwners = []string{"NONE"}
			}

			// Get values from sync.Maps with defaults if empty
			envList := []string{"NONE"}
			if envVal, ok := uniqueEnv.Load(docID); ok && len(envVal.([]string)) > 0 {
				envList = envVal.([]string)
			}

			if len(enhancedUniqueApps) <= 0 {
				enhancedUniqueApps = []string{"NONE"}
			}

			if len(enhancedUniqueTeams) <= 0 {
				enhancedUniqueTeams = []string{"NONE"}
			}

			softwareList := []string{"NONE"}
			if softwareVal, ok := uniqueSoftware.Load(docID); ok && len(softwareVal.([]string)) > 0 {
				softwareList = softwareVal.([]string)
			}

			depList := []string{"NONE"}
			if depVal, ok := uniqueDeployment.Load(docID); ok && len(depVal.([]string)) > 0 {
				depList = depVal.([]string)
			}

			compList := []string{"NONE"}
			if compVal, ok := uniqueCompliance.Load(docID); ok && len(compVal.([]string)) > 0 {
				compList = compVal.([]string)
			}

			sensList := []string{"NONE"}
			if sensVal, ok := uniqueSensitivity.Load(docID); ok && len(sensVal.([]string)) > 0 {
				sensList = sensVal.([]string)
			}

			ccList := []string{"NONE"}
			if ccVal, ok := uniqueCostCenter.Load(docID); ok && len(ccVal.([]string)) > 0 {
				ccList = ccVal.([]string)
			}

			ttlList := []string{"NONE"}
			if ttlVal, ok := uniqueTTL.Load(docID); ok && len(ttlVal.([]string)) > 0 {
				ttlList = ttlVal.([]string)
			}

			uaList := []string{"NONE"}
			if uaVal, ok := uniqueUsrAgent.Load(docID); ok && len(uaVal.([]string)) > 0 {
				uaList = uaVal.([]string)
			}

			relatedResourceList := make([]rcontext.RelatedResource, 0)
			if rList, ok := resourceContext.GetRelatedResourceList(docID); ok {
				relatedResourceList = rList
			}

			relatedResources, err = json.Marshal(relatedResourceList)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			commitInfo, err := json.Marshal(resourceContextDoc.CommitInfo)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			resourceContextDoc.LastCollectedAt = lastCollectedAt
			resourceContextDoc.UpdatedTime = elastic.DateTime(currentTime)
			resourceContextDoc.ID = docID

			resourceContext.SetResourceContextInsertDoc(docID, *resourceContextDoc)

			if persistEnhancerData {
				identity.ProcessResourceContextForIdentityCreation(&identitiesMap, resourceContextDoc, docID, &uniqueUsers, resourceContext, &identityCount, &identityMapLock)
			}

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			cloudResourceUpdateMetadata := `{"update": {"_id": "` + resourceContextDoc.CloudResourceDocID + `"}}`

			var cloudResourceUpdateDoc strings.Builder
			cloudResourceUpdateDoc.WriteString(`{"doc":{"relatedResources":`)
			cloudResourceUpdateDoc.Write(relatedResources)
			cloudResourceUpdateDoc.WriteString(`, "contextLabels":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(resourceContextDoc.ContextLabels, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "owner":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueOwners, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "environment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(envList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "app":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueApps, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "software":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(softwareList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "deployment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(depList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "compliance":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(compList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "sensitivity":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(sensList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "costCenter":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ccList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "ttl":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ttlList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "team":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueTeams, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "users":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uniqueUsers, `","`))
			cloudResourceUpdateDoc.WriteString(`"],  "stageCompleted":["dc","enhancer"],"userAgent":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uaList, `","`))
			cloudResourceUpdateDoc.WriteString(`"],"extContext": {"commitInfo": `)
			cloudResourceUpdateDoc.Write(commitInfo)
			cloudResourceUpdateDoc.WriteString(`}}}`)

			bulkMutex.Lock()
			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")

			bulkCloudResourceRequest.WriteString(cloudResourceUpdateMetadata)
			bulkCloudResourceRequest.WriteString("\n")
			bulkCloudResourceRequest.WriteString(cloudResourceUpdateDoc.String())
			bulkCloudResourceRequest.WriteString("\n")

			recordsCount++

			if recordsCount >= maxRecords {

				if persistEnhancerData {

					if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
						bulkMutex.Unlock()
						return
					}

					logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records	", []string{tenantID}, lastCollectedAt)

					if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
						bulkMutex.Unlock()
						return
					}

					logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)
				}

				recordsCount = 0
				bulkResourceContextRequest.Reset()
				bulkCloudResourceRequest.Reset()
			}

			bulkMutex.Unlock()

			uniqueMap = nil
			relatedResources = nil
			commitInfo = nil
			resourceContextInsertDoc = nil

		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage2)

	if profiler != nil {
		profiler.RecordStage("stage2_completed")
		profiler.SaveHeapProfile("stage2_completed")
	}

	if recordsCount > 0 && persistEnhancerData {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)
	}

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {

		if int(identityCount) > 0 {
			err := identity.InsertIdentites(&identitiesMap, resourceContext.TenantID)
			if err != nil {
				return
			}
		}

		err := identity.ProcessDeletedResourceContextDocs(elastic.DateTime(currentTime), resourceContext.TenantID, resourceContext.ServiceID)
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Starting identity primary email updating process")

		err = identity.UpdatePrimaryEmailForNonEnhancerIdentities(resourceContext, elastic.DateTime(currentTime))
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Completed identity primary email updating process")

		logger.Print(logger.INFO, "Starting identity status updating process", []string{tenantID}, lastCollectedAt)

		err = identity.UpdateIdentityType(resourceContext)
		if err != nil {
			return
		}

		global.ProcessGlobalApps(resourceContext)
		global.AIRejectedGlobalContext(resourceContext)
		global.SetGlobalOrgContext(resourceContext)

		logger.Print(logger.INFO, "Completed identity status updating process", []string{tenantID}, lastCollectedAt)
	}

	logger.Print(logger.INFO, "Completed post process stage 2", lastCollectedAt)

	if config.Environment == config.PROD_ENV {
		logger.Print(logger.INFO, "Sleeping 5 mins for data sync", []string{tenantID}, lastCollectedAt)
		time.Sleep(5 * time.Minute)
	}

	logger.Print(logger.INFO, "Enhancer completed for tenant", []string{tenantID}, lastCollectedAt)
}
