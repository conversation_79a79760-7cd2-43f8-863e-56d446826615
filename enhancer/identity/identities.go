package identity

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/resource"
	"github.com/precize/logger"
)

const (
	MAX_RECORDS                   = 10000
	IDENTITY_INSERTION_MAXRECORDS = 1000
)

func ProcessResourceContextForIdentityCreation(identityMap *sync.Map, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextDocID string, uniqueUsers *[]string, r *rcontext.ResourceContext, identityCount *int32, identityMapLock *sync.RWMutex) {

	usersMap := make(map[string]struct{})

	identityType := ""
	for i, rctx := range resourceContextDoc.DefinedOwners {
		if rctx.Type != "" && strings.Contains(rctx.Type, contextutils.TAG_PREFIX) {
			identityType = common.TAGGED_IDENTITY_TYPE
		} else {
			identityType = common.DEFINED_IDENTITY_TYPE
		}
		createIdentity(&rctx, identityMap, resourceContextDocID, identityType, resourceContextDoc, usersMap, r, identityCount, identityMapLock)
		resourceContextDoc.DefinedOwners[i] = rctx
	}
	for i, rctx := range resourceContextDoc.CodeOwners {
		if rctx.Type != "" {
			if strings.Contains(rctx.Type, common.GITHUB_OWNER_USER_TYPE) {
				identityType = common.GITHUB_IDENTITY_TYPE
			} else if strings.Contains(rctx.Type, common.GITLAB_OWNER_USER_TYPE) {
				identityType = common.GITLAB_IDENTITY_TYPE
			} else if strings.Contains(rctx.Type, common.BITBUCKET_OWNER_USER_TYPE) {
				identityType = common.BITBUCKET_IDENTITY_TYPE
			}
		}
		createIdentity(&rctx, identityMap, resourceContextDocID, identityType, resourceContextDoc, usersMap, r, identityCount, identityMapLock)
		resourceContextDoc.CodeOwners[i] = rctx
	}

	for i, rctx := range resourceContextDoc.DerivedOwners {
		if rctx.Event != nil && strings.Contains(rctx.Type, common.ACTIVITY_USER_TYPE) {
			if eventTime, err := elastic.ParseDateTime(rctx.Event.Time); err == nil {

				// EX-EMPLOYEE activity owner with event time before 3 months to be skipped only if there are other activity owners present
				if eventTime.Before(time.Now().AddDate(0, -6, 0)) {
					identityID := ""

					if len(rctx.IdentityId) > 0 {
						identityID = rctx.IdentityId
					} else {
						identityID = rctx.Name
					}

					if strings.Contains(identityID, contextutils.EX_EMPLOYEE_PREFIX) && (len(resourceContextDoc.DerivedOwners) > 1 || len(resourceContextDoc.DefinedOwners) > 0) {
						continue
					}
				}
			}

			identityType = common.ACTIVITY_IDENTITY_TYPE
		} else {
			identityType = common.DERIVED_IDENTITY_TYPE
		}
		createIdentity(&rctx, identityMap, resourceContextDocID, identityType, resourceContextDoc, usersMap, r, identityCount, identityMapLock)
		resourceContextDoc.DerivedOwners[i] = rctx
	}

	for i, rctx := range resourceContextDoc.InheritedOwners {
		identityId, isServiceIdentity := deriveIdentityIdFromRctxItem(&rctx)
		if identityStatus, ok := updateIdentityStatusBasedOnRctxItem(r, identityId, isServiceIdentity); ok {
			rctx.IdentityStatus = common.IntPtr(identityStatus)
		}
		resourceContextDoc.InheritedOwners[i] = rctx
	}

	for i, rctx := range resourceContextDoc.CostOwners {
		identityId, isServiceIdentity := deriveIdentityIdFromRctxItem(&rctx)
		if identityStatus, ok := updateIdentityStatusBasedOnRctxItem(r, identityId, isServiceIdentity); ok {
			rctx.IdentityStatus = common.IntPtr(identityStatus)
		}
		resourceContextDoc.CostOwners[i] = rctx
	}

	for i, rctx := range resourceContextDoc.SecurityOwners {
		identityId, isServiceIdentity := deriveIdentityIdFromRctxItem(&rctx)
		if identityStatus, ok := updateIdentityStatusBasedOnRctxItem(r, identityId, isServiceIdentity); ok {
			rctx.IdentityStatus = common.IntPtr(identityStatus)
		}
		resourceContextDoc.SecurityOwners[i] = rctx
	}

	for i, rctx := range resourceContextDoc.OpsOwners {
		identityId, isServiceIdentity := deriveIdentityIdFromRctxItem(&rctx)
		if identityStatus, ok := updateIdentityStatusBasedOnRctxItem(r, identityId, isServiceIdentity); ok {
			rctx.IdentityStatus = common.IntPtr(identityStatus)
		}
		resourceContextDoc.OpsOwners[i] = rctx
	}

	for user := range usersMap {
		*uniqueUsers = append(*uniqueUsers, user)
	}
}

func createIdentity(rctx *common.ResourceContextItem, identityMap *sync.Map, resourceContextDocID, identityType string, resourceContextDoc *common.ResourceContextInsertDoc, usersMap map[string]struct{}, r *rcontext.ResourceContext, identityCount *int32, identityMapLock *sync.RWMutex) {

	nonHumanIdentitySuffixes := []string{
		contextutils.AWSSERVICE_USER_SUFFIX,
		contextutils.ACCOUNT_USER_SUFFIX,
		contextutils.IAM_USER_SUFFIX,
		contextutils.IAM_ROLE_SUFFIX,
		contextutils.APP_USER_SUFFIX,
		contextutils.SERVICEACCOUNT_USER_SUFFIX,
	}

	var (
		identityId                string
		identityName              string
		serviceIdentity           bool
		insertIdentityIntoUserMap string
		// childIdentityId           string
	)

	if len(rctx.ChildIdentityID) > 0 {
		identityId = rctx.ChildIdentityID
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
			serviceIdentity = true
		}

		if addr, err := common.ParseAddress(rctx.Name); err == nil {
			identityName = addr.Name
		} else {
			identityName = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
		}

		if len(rctx.IdentityId) > 0 {
			insertIdentityIntoUserMap = rctx.IdentityId
		}
	} else if len(rctx.IdentityId) > 0 {
		identityId = rctx.IdentityId
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
			serviceIdentity = true
		}

		if addr, err := common.ParseAddress(rctx.Name); err == nil {
			identityName = addr.Name
		} else {
			identityName = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
		}
		insertIdentityIntoUserMap = identityId
	} else if strings.Contains(rctx.Name, "<") && strings.Contains(rctx.Name, ">") {
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {

			serviceIdentity = true
			identityId = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
			identityName = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
		} else if strings.Contains(rctx.Name, "@") {
			if addr, err := common.ParseAddress(rctx.Name); err == nil {
				identityId = addr.Address
				identityName = addr.Name
			}
		}
		insertIdentityIntoUserMap = identityId
	} else {
		// for identity id's with only name
		identityId = rctx.Name
		identityName = rctx.Name
		insertIdentityIntoUserMap = identityId
	}

	// some identities like role, etc. might have uppercase characters and space.
	identityId = strings.ToLower(strings.ReplaceAll(identityId, " ", ""))

	// Update Resource Context identityId only if it is empty, if it is not empty, it means it is already has correct value
	if len(rctx.IdentityId) <= 0 {
		rctx.IdentityId = identityId
	}

	insertIdentityIntoUserMap = strings.ToLower(strings.ReplaceAll(insertIdentityIntoUserMap, " ", ""))
	usersMap[insertIdentityIntoUserMap] = struct{}{}

	if len(rctx.ChildIdentityID) > 0 {
		rctx.ChildIdentityID = strings.ToLower(strings.ReplaceAll(rctx.ChildIdentityID, " ", ""))
	}

	if len(rctx.IdentityId) > 0 {
		rctx.IdentityId = strings.ToLower(strings.ReplaceAll(rctx.IdentityId, " ", ""))
	}

	// update rctx identityStatus
	if identityStatus, ok := updateIdentityStatusBasedOnRctxItem(r, identityId, serviceIdentity); ok {
		rctx.IdentityStatus = common.IntPtr(identityStatus)
	}

	identityDocID := common.GenerateCombinedHashIDCaseSensitive(resourceContextDoc.TenantID, common.IdStrToCspStrMap[strconv.Itoa(resourceContextDoc.ServiceID)], strings.ToLower(identityId), resourceContextDoc.Account, identityType)

	loadedIdentity, alreadyInserted := identityMap.Load(identityDocID)

	var (
		eventTime                  time.Time
		alreadyInsertedIdentity, _ = loadedIdentity.(common.IdentitiesDoc)
	)

	if rctx.Event != nil {
		if !rctx.Event.IndirectEvent {
			time, err := common.FormatElasticTime(rctx.Event.Time)
			if err == nil {
				eventTime, err = elastic.ParseDateTime(time)
			}
		}
	}

	if !alreadyInserted || eventTime.UnixMilli() > alreadyInsertedIdentity.LastWriteTime {

		if len(identityName) <= 0 {
			identityName = identityId
		}

		if len(identityId) <= 0 {
			return
		}

		identityName = strings.TrimPrefix(identityName, contextutils.EX_EMPLOYEE_PREFIX)
		identityName = strings.TrimPrefix(identityName, contextutils.INVALID_EMPLOYEE_PREFIX)
		identityName = common.ConvertToTitleCase(identityName)

		additionalInfo := map[string]string{
			"createdBy":           "enhancer",
			"lastWriteDuringScan": elastic.DateTime(eventTime),
			"resCtxId":            resourceContextDocID,
		}

		jsonBytes, err := json.Marshal(additionalInfo)
		if err != nil {
			logger.Print(logger.ERROR, "Error converting to JSON", err)
			return
		}

		// Convert JSON bytes to string
		addnInfoString := string(jsonBytes)

		identityDoc := common.IdentitiesDoc{
			IdentityID:     identityId,
			Type:           identityType,
			ID:             identityDocID,
			Deleted:        false,
			UIDeleted:      false,
			AdditionalInfo: addnInfoString,
			UpdatedDate:    resourceContextDoc.UpdatedTime,
			Name:           identityName,
			ServiceCode:    common.IdStrToCspStrMap[strconv.Itoa(resourceContextDoc.ServiceID)],
			TenantID:       resourceContextDoc.TenantID,
			AccountID:      resourceContextDoc.Account,
			CreatedDate:    resourceContextDoc.UpdatedTime,
			Permission:     -1, // None
			IdentityStatus: common.UNKNOWN_IDENTITY_STATUS,
		}

		if serviceIdentity {
			identityDoc.IdentityStatus = common.VALID_IDENTITY_STATUS
		}

		if identityType == common.ACTIVITY_IDENTITY_TYPE {
			// Write permission
			identityDoc.Permission = -1
		}

		if primaryEmail, ok := r.GetChildPrimaryEmail(identityId); !ok && !serviceIdentity {

			var isPrimaryDomain bool
			for _, primaryDomain := range r.PrimaryDomains {
				if strings.HasSuffix(strings.ToLower(identityId), strings.ToLower(primaryDomain)) {
					isPrimaryDomain = true
					break
				}
			}

			if !isPrimaryDomain {
				if _, ok := r.GetChildPrimaryEmail(identityId); !ok {
					primaryEmail := emailutils.DerivePrimaryIdentityFromPartner(identityId, identityName, r)
					if len(primaryEmail) > 0 && identityId != primaryEmail {
						if ok := r.SetChildPrimaryEmail(identityId, primaryEmail); ok {
							identityDoc.PrimaryEmail = primaryEmail
						}
					}
				}
			}
		} else {
			identityDoc.PrimaryEmail = primaryEmail
		}

		// if event time is present update last write activity and last write time

		if !eventTime.IsZero() {
			identityDoc.LastWriteTime = eventTime.UnixMilli()
			event := &common.Event{
				Name:   rctx.Event.Name,
				Region: rctx.Event.Region,
				Time:   elastic.DateTime(eventTime),
			}
			identityDoc.LastWriteActivity = event
		}

		identityMapLock.Lock()
		defer identityMapLock.Unlock()

		loadedIdentity, alreadyInserted = identityMap.Load(identityDocID)

		if !alreadyInserted || eventTime.UnixMilli() > alreadyInsertedIdentity.LastWriteTime {

			if atomic.LoadInt32(identityCount) > IDENTITY_INSERTION_MAXRECORDS {

				InsertIdentites(identityMap, resourceContextDoc.TenantID)
				atomic.StoreInt32(identityCount, 0)

			}

			atomic.AddInt32(identityCount, 1)
			identityMap.Store(identityDocID, identityDoc)
		}
	}
}

func InsertIdentites(identityMap *sync.Map, tenantID string) (err error) {
	var (
		identityDocIds        = make([]string, 0)
		bulkIdentitiesRequest string
		recordsCount          int
	)

	identityMap.Range(func(key, value any) bool {
		identityDocIds = append(identityDocIds, key.(string))
		return true
	})

	if len(identityDocIds) <= 0 {
		return
	}

	identityQuery := `{"_source":["identityId","id","lastWriteActivity","lastWriteTime","lastReadActivity","lastReadTime","createdDate"],"query":{"bool":{"must":[{"terms":{"id.keyword":["` + strings.Join(identityDocIds, `","`) + `"]}}]}}}`
	identityDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery)
	if err != nil {
		return
	}

	existingCloudIdentitiesMap := make(map[string]common.IdentitiesDoc)
	for _, identityDoc := range identityDocs {
		var identity common.IdentitiesDoc
		jsonBytes, err := json.Marshal(identityDoc)
		if err != nil {
			return err
		}
		err = json.Unmarshal(jsonBytes, &identity)
		if err != nil {
			return err
		}
		existingCloudIdentitiesMap[identity.ID] = identity
	}

	identityMap.Range(func(k, v any) bool {
		key := k.(string)
		identity := v.(common.IdentitiesDoc)

		if eIdentity, ok := existingCloudIdentitiesMap[key]; ok {

			if eIdentity.LastReadActivity != nil && (identity.LastReadActivity == nil || eIdentity.LastReadTime > identity.LastReadTime) {
				// Keep eidentity last read if identity last read is empty or identity last read is before eIdentity last read time

				if eIdentity.LastReadActivity.Time != "" {
					// Convert time format for LastReadActivity
					if parsedTime, err := elastic.ParseDateTime(eIdentity.LastReadActivity.Time); err == nil {
						eIdentity.LastReadActivity.Time = elastic.DateTime(parsedTime)
					}
				}

				identity.LastReadActivity = eIdentity.LastReadActivity
			}

			if eIdentity.LastReadTime != 0 && (identity.LastReadTime == 0 || eIdentity.LastReadTime > identity.LastReadTime) {
				identity.LastReadTime = eIdentity.LastReadTime
			}

			if eIdentity.LastWriteActivity != nil && (identity.LastWriteActivity == nil || eIdentity.LastWriteTime > identity.LastWriteTime) {
				// Keep eidentity last write if identity last write is empty or identity last write is before eIdentity last write time

				if eIdentity.LastWriteActivity.Time != "" {
					// Convert time format for LastWriteActivity
					if parsedTime, err := elastic.ParseDateTimeWithSeconds(eIdentity.LastWriteActivity.Time); err == nil {
						eIdentity.LastWriteActivity.Time = elastic.DateTime(parsedTime)
					}
				}
				identity.LastWriteActivity = eIdentity.LastWriteActivity
			}

			if eIdentity.LastWriteTime != 0 && (identity.LastWriteTime == 0 || eIdentity.LastWriteTime > identity.LastWriteTime) {
				identity.LastWriteTime = eIdentity.LastWriteTime
			}

			if eIdentity.CreatedDate != "" {
				identity.CreatedDate = eIdentity.CreatedDate
			}

			identityMap.Store(key, identity)
		}

		identitiesInsertMetadata := `{"index": {"_id": "` + key + `"}}`
		identitiesInsertDoc, err := json.Marshal(identity)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal document", err)
			return true
		}

		bulkIdentitiesRequest = bulkIdentitiesRequest + identitiesInsertMetadata + "\n" + string(identitiesInsertDoc) + "\n"
		recordsCount++
		return true
	})

	if recordsCount > 0 {
		if err = elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
			return
		}

		logger.Print(logger.INFO, "Identities bulk insertion API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		recordsCount = 0
		time.Sleep(time.Second * 2)
	}

	// clear identitiesMap
	identityMap.Range(func(key, _ any) bool {
		identityMap.Delete(key)
		return true
	})

	return
}

func ProcessDeletedResourceContextDocs(currentTime, tenantID string, serviceID string) error {

	var (
		searchAfter           any
		identityQuery         = `{"query":{"bool":{"must":[{"match":{"deleted":"false"}},{"bool":{"should":[{"bool":{"must_not":{"exists":{"field":"updatedDate"}}}},{"range":{"updatedDate":{"lt":"` + currentTime + `"}}}],"minimum_should_match":1}},{"terms":{"type.keyword":["BITBUCKET_IDENTITY","GITLAB_IDENTITY","GITHUB_IDENTITY","TAGGED_IDENTITY","DEFINED_IDENTITY","ACTIVITY_IDENTITY","DERIVED_IDENTITY","BITBUCKET_USER","GITLAB_USER","GITHUB_USER","TAGGED_USER","DEFINED_USER","ACTIVITY_USER","DERIVED_USER"]}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"serviceCode.keyword":"` + common.IdStrToCspStrMap[serviceID] + `"}},{"wildcard":{"additionalInfo.keyword":"*resCtxId*"}}]}}}`
		bulkIdentitiesRequest string
		recordsCount          int
	)

	for {

		identityDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery, searchAfter)
		if err != nil {
			return err
		}

		if len(identityDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, identityDoc := range identityDocs {
			var identity common.IdentitiesDoc
			jsonBytes, err := json.Marshal(identityDoc)
			if err != nil {
				return err
			}
			err = json.Unmarshal(jsonBytes, &identity)
			if err != nil {
				return err
			}

			// to remove [X] and [I] from deleted identities
			identity.Name = strings.Trim(identity.Name, contextutils.EX_EMPLOYEE_PREFIX)
			identity.Name = strings.Trim(identity.Name, contextutils.INVALID_EMPLOYEE_PREFIX)

			identitiesUpdateMetadata := `{"update": {"_id": "` + identity.ID + `"}}`
			identitiesUpdateDoc := `{"doc":{"deleted":true,"updatedDate":"` + currentTime + `","name":"` + identity.Name + `"}}`
			bulkIdentitiesRequest = bulkIdentitiesRequest + identitiesUpdateMetadata + "\n" + identitiesUpdateDoc + "\n"
			recordsCount++

			if recordsCount > MAX_RECORDS {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
					return err
				}
				logger.Print(logger.INFO, "Identities bulk update deleted flag successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
				recordsCount = 0
				bulkIdentitiesRequest = ""
				time.Sleep(time.Second * 1)
			}
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
			return err
		}
		logger.Print(logger.INFO, "Identities bulk update deleted flag successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
		recordsCount = 0
		time.Sleep(time.Second * 1)
	}

	return nil
}

func UpdatePrimaryEmailForNonEnhancerIdentities(resourceContext *rcontext.ResourceContext, currentTime string) error {

	var (
		bulkIdentitiesRequest string
		recordsCount          int
		maxRecords            = 5000
		childToPrimaryEmail   = make(map[string]string)
	)

	resourceContext.RangeChildPrimaryEmail(func(childEmail, primaryEmail string) bool {
		childToPrimaryEmail[childEmail] = primaryEmail
		return true
	})

	childToPrimaryEmail = resolveMappings(childToPrimaryEmail)

	for childEmail, primaryEmail := range childToPrimaryEmail {

		var (
			searchAfter   any
			identityQuery = `{"_source":["identityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"identityId.keyword":"` + childEmail + `"}}],"must_not":[]}}}`
		)

		for {
			identitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery, searchAfter)
			if err != nil {
				return err
			}

			if len(identitiesDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for identityDocID := range identitiesDocs {

				identitiesUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
				identitiesUpdateDoc := `{"doc":{"primaryEmail":"` + primaryEmail + `"}}`
				bulkIdentitiesRequest = bulkIdentitiesRequest + identitiesUpdateMetadata + "\n" + identitiesUpdateDoc + "\n"
				recordsCount++

			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
				return err
			}
			logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})
			recordsCount = 0
			bulkIdentitiesRequest = ""
		}

	}

	// If there is no identity with primaryEmail, create a new derived identity
	for childEmail, primaryEmail := range childToPrimaryEmail {

		var (
			identityAggsQuery = `{"query":{"bool":{"must":[{"match":{"identityId.keyword":"` + primaryEmail + `"}},{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"deleted":"false"}}]}},"size":1}`
		)

		aggResponse, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityAggsQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error executing aggregation query", []string{err.Error()})
			return err
		}

		if len(aggResponse) <= 0 {
			if addr, err := common.ParseAddress(primaryEmail); err == nil {

				identityType := common.DERIVED_IDENTITY_TYPE
				identityDocID := common.GenerateCombinedHashIDCaseSensitive(resourceContext.TenantID, common.IdStrToCspStrMap[resourceContext.ServiceID], strings.ToLower(addr.Address), identityType)

				identityName := common.GetFormattedNameFromEmail(addr.Address)
				if userResources, ok := resourceContext.GetUserResource(addr.Address); ok && len(userResources.Name) > len(identityName) {
					identityName = userResources.Name
				}

				additionalInfo := map[string]string{
					"createdBy": "enhancer",
				}

				jsonBytes, err := json.Marshal(additionalInfo)
				if err != nil {
					logger.Print(logger.ERROR, "Error converting to JSON", err)
					return err
				}

				// Convert JSON bytes to string
				addnInfoString := string(jsonBytes)

				identityDoc := common.IdentitiesDoc{
					IdentityID:     primaryEmail,
					Type:           identityType,
					ID:             identityDocID,
					UpdatedDate:    currentTime,
					Name:           identityName,
					ServiceCode:    common.IdStrToCspStrMap[resourceContext.ServiceID],
					AdditionalInfo: addnInfoString,
					Deleted:        false,
					UIDeleted:      false,
					TenantID:       resourceContext.TenantID,
					CreatedDate:    currentTime,
					Permission:     -1, // None
				}

				if isNonHuman, ok := resourceContext.GetAliasUpdate(addr.Address); ok {
					identityDoc.IdentityStatus = DetermineIdentityStatusForEmail(resourceContext, addr.Address, isNonHuman)
				}

				idenAggQuery := `{"query":{"bool":{"must":[{"match":{"identityId.keyword":"` + childEmail + `"}},{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}}]}},"size":0,"aggs":{"by_accountId":{"terms":{"field":"accountId.keyword","order":{"_key":"desc"},"size":1}},"by_deletionstatus":{"terms":{"field":"deleted"}}}}`
				idenAggQueryResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.IDENTITIES_INDEX}, idenAggQuery)
				if err != nil {
					return err
				}

				if crAggsResp, ok := idenAggQueryResp["by_accountId"].(map[string]any); ok {
					if buckets, ok := crAggsResp["buckets"].([]any); ok {
						if len(buckets) > 0 {
							if bucket, ok := buckets[0].(map[string]any); ok {
								if key, ok := bucket["key"].(string); ok {
									identityDoc.AccountID = key
								}
							}
						}
					}
				}

				isActiveChildIdentityPresent := false
				if crAggsResp, ok := idenAggQueryResp["by_deletionstatus"].(map[string]any); ok {
					if buckets, ok := crAggsResp["buckets"].([]any); ok {
						if len(buckets) > 0 {

							for _, bucket := range buckets {
								if bucket, ok := bucket.(map[string]any); ok {
									if key, ok := bucket["key_as_string"].(string); ok && key == "false" {
										if count, ok := bucket["doc_count"].(float64); ok && count > 0 {
											isActiveChildIdentityPresent = true
										}
									}
								}
							}
						}
					}
				}

				if !isActiveChildIdentityPresent {
					identityDoc.Deleted = true
					identityDoc.UIDeleted = true
				}

				identitiesInsertMetadata := `{"index": {"_id": "` + identityDocID + `"}}`
				identitiesInsertDoc, err := json.Marshal(identityDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Failed to marshal document", err)
					return err
				}

				bulkIdentitiesRequest = bulkIdentitiesRequest + identitiesInsertMetadata + "\n" + string(identitiesInsertDoc) + "\n"
				recordsCount++

			}

			if recordsCount >= maxRecords {
				if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
					return err
				}
				logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

				recordsCount = 0
				bulkIdentitiesRequest = ""
			}
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
			return err
		}
		logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})
	}

	// Sleeping for data sync
	time.Sleep(time.Second * 1)

	return nil
}

func resolveMappings(childToPrimaryEmail map[string]string) map[string]string {
	resolved := make(map[string]string)

	var findUltimate func(string) string
	findUltimate = func(email string) string {
		if final, exists := resolved[email]; exists {
			return final
		}
		original := email
		visited := make(map[string]bool)

		for childToPrimaryEmail[email] != "" {
			if childToPrimaryEmail[email] == email {
				break
			}

			if visited[email] {
				break
			}

			visited[email] = true
			email = childToPrimaryEmail[email]
		}
		resolved[original] = email
		return email
	}

	for key, val := range childToPrimaryEmail {
		if len(val) > 0 {
			resolved[key] = findUltimate(key)
		} else {
			resolved[key] = ""
		}
	}

	return resolved
}

func UpdateIdentityType(resourceContext *rcontext.ResourceContext) error {

	var (
		searchAfter                 any
		serviceCode                 = common.IdStrToCspStrMap[resourceContext.ServiceID]
		cloudUserIdentitiesQuery    = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"serviceCode.keyword":["` + serviceCode + `","okta","openai"]}}],"must_not":[{"terms":{"type.keyword":["APP_REGISTRATION","TRUSTED_ENTITY","SERVICE_ACCOUNT","AWS_IAM_ROLE","GRAPH_APPLICATION","OPEN_AI_SERVICE_ACCOUNT"]}}]}}}`
		bulkIdentitiesStatusRequest string
		recordsCount                int
		emailToNameMap              = make(map[string]string)
		searchRctxForIdentityStatus = make(map[string][]common.IdentitiesDoc)
	)

	for {
		cloudUserIdentitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, cloudUserIdentitiesQuery, searchAfter)
		if err != nil {
			return err
		}

		if len(cloudUserIdentitiesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		nameList := make(map[string][]string)

		for identityDocID, cloudUserIdentitiesDoc := range cloudUserIdentitiesDocs {
			identitiesDocBytes, err := json.Marshal(cloudUserIdentitiesDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling identities doc to JSON", []string{resourceContext.TenantID}, err)
				return err
			}

			var identitiesDoc common.IdentitiesDoc
			if err = json.Unmarshal(identitiesDocBytes, &identitiesDoc); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{identitiesDoc.TenantID}, err)
				return err
			}

			if len(identitiesDoc.IdentityID) > 0 {
				aiInput := common.GetEmailNameWithoutSpecialCharacters(identitiesDoc.IdentityID)
				if identitiesDoc.Name != "" {
					aiInput = identitiesDoc.Name
				}

				if addr, err := common.ParseAddress(identitiesDoc.PrimaryEmail); err == nil {
					// human/ nonhuman email/ service account

					if strings.Contains(addr.Address, ".gserviceaccount.com") {
						identitiesDoc.IdentityStatus = common.VALID_IDENTITY_STATUS

					} else {

						if isNonHuman, ok := resourceContext.GetAliasUpdate(addr.Address); ok {
							identitiesDoc.IdentityStatus = DetermineIdentityStatusForEmail(resourceContext, addr.Address, isNonHuman)
						} else {
							emailToNameMap[addr.Address] = identitiesDoc.Name
							nameList[aiInput] = append(nameList[aiInput], identityDocID+":"+addr.Address)
						}
					}

				} else if addr, err := common.ParseAddress(identitiesDoc.IdentityID); err == nil {
					// human/ nonhuman email/ service account

					if strings.Contains(addr.Address, ".gserviceaccount.com") {
						identitiesDoc.IdentityStatus = common.VALID_IDENTITY_STATUS

					} else {

						if isNonHuman, ok := resourceContext.GetAliasUpdate(addr.Address); ok {
							identitiesDoc.IdentityStatus = DetermineIdentityStatusForEmail(resourceContext, addr.Address, isNonHuman)
						} else {
							emailToNameMap[addr.Address] = identitiesDoc.Name
							nameList[aiInput] = append(nameList[aiInput], identityDocID+":"+addr.Address)
						}
					}
				} else {
					// iam role, human name, nonhuman name, application, etc

					matchFound := true

					if _, ok := resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.IdentityID) + strings.ToLower(contextutils.SERVICEACCOUNT_USER_SUFFIX)); !ok {
						if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.IdentityID) + strings.ToLower(contextutils.ACCOUNT_USER_SUFFIX)); !ok {
							if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.IdentityID) + strings.ToLower(contextutils.IAM_ROLE_SUFFIX)); !ok {
								if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.IdentityID) + strings.ToLower(contextutils.APP_USER_SUFFIX)); !ok {
									if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.IdentityID) + strings.ToLower(contextutils.AWSSERVICE_USER_SUFFIX)); !ok {
										matchFound = false
									}
								}
							}
						}
					}

					if !matchFound {
						matchFound = true

						if _, ok := resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.Name) + strings.ToLower(contextutils.SERVICEACCOUNT_USER_SUFFIX)); !ok {
							if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.Name) + strings.ToLower(contextutils.ACCOUNT_USER_SUFFIX)); !ok {
								if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.Name) + strings.ToLower(contextutils.IAM_ROLE_SUFFIX)); !ok {
									if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.Name) + strings.ToLower(contextutils.APP_USER_SUFFIX)); !ok {
										if _, ok = resourceContext.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(identitiesDoc.Name) + strings.ToLower(contextutils.AWSSERVICE_USER_SUFFIX)); !ok {
											matchFound = false
										}
									}
								}
							}
						}
					}

					if !matchFound {
						additionalInfo := make(map[string]any)
						if err := json.Unmarshal([]byte(identitiesDoc.AdditionalInfo), &additionalInfo); err != nil {
							continue
						}

						if identitiesDoc.Type == common.AWS_USER_IDENTITY_TYPE || identitiesDoc.Type == common.AWS_ROOT_USER_IDENTITY_TYPE {
							matchFound = true
						} else if rctxID, ok := additionalInfo["resCtxId"].(string); ok {
							if rctx, ok := resourceContext.GetResourceContextInsertDoc(rctxID); ok {
								identitiesDoc.IdentityStatus = GetIdentityStatusFromRctx(rctx, identitiesDoc.IdentityID)
							} else {
								searchRctxForIdentityStatus[rctxID] = append(searchRctxForIdentityStatus[rctxID], identitiesDoc)
								continue
							}
						}
					}

					if matchFound {
						identitiesDoc.IdentityStatus = common.VALID_IDENTITY_STATUS
					} else {

						if identitiesDoc.Type == "APP_REGISTRATION" || identitiesDoc.Type == "SERVICE_ACCOUNT" || identitiesDoc.Type == "AWS_IAM_ROLE" || identitiesDoc.Type == "GRAPH_APPLICATION" || identitiesDoc.Type == "OPEN_AI_SERVICE_ACCOUNT" || identitiesDoc.Type == "TRUSTED_ENTITY" {
							identitiesDoc.IdentityStatus = common.VALID_IDENTITY_STATUS
						} else if identitiesDoc.IdentityStatus == -1 {
							// move unknown identities to ambiguouys
							identitiesDoc.IdentityStatus = common.AMBIGUOUS_IDENTITY_STATUS
						}
					}
				}
			}

			identityStatusUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
			identityStatusUpdateDoc := `{"doc":{"identityStatus":` + fmt.Sprint(identitiesDoc.IdentityStatus) + `}}`

			bulkIdentitiesStatusRequest = bulkIdentitiesStatusRequest + identityStatusUpdateMetadata + "\n" + identityStatusUpdateDoc + "\n"
			recordsCount++

			if recordsCount > MAX_RECORDS {
				if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
					return err
				}
				logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

				recordsCount = 0
				bulkIdentitiesStatusRequest = ""
			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Identity type bulk API Successful for ", []string{resourceContext.TenantID}, recordsCount)

			recordsCount = 0
			bulkIdentitiesStatusRequest = ""
		}

		batchSize := 10
		emailNameMap := make(map[string]string)
		for email, name := range emailToNameMap {
			emailNameMap[email] = name
			if len(emailNameMap) >= batchSize {
				err := ProcessBatchEmailValidity(emailNameMap, resourceContext)
				if err != nil {
					emailNameMap = make(map[string]string)
					continue
				}

				emailNameMap = make(map[string]string)
			}
		}

		if len(emailNameMap) > 0 {
			if err := ProcessBatchEmailValidity(emailNameMap, resourceContext); err != nil {
				//skip, if error is returned
			}
		}

		if len(nameList) > 0 {

			exampleNames := map[string]bool{
				"John Doe":    true,
				"Engineering": false,
				"Satyam":      true,
				"DevOps":      false,
			}

			// fetch samples
			context.PopulateExampleNames(resourceContext.TenantID, exampleNames)
			resp := common.HumanOrNonHumanName(nameList, exampleNames, resourceContext.TenantID)

			for input, hasName := range resp {
				if identityDocs, ok := nameList[input]; ok {
					for _, identityDoc := range identityDocs {

						splitParts := strings.Split(identityDoc, ":")
						if len(splitParts) == 2 {

							identityDocID := splitParts[0]
							identityID := splitParts[1]
							identityStatus := DetermineIdentityStatusForEmail(resourceContext, identityID, !hasName)

							identityStatusUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
							identityStatusUpdateDoc := `{"doc":{"identityStatus":` + fmt.Sprint(identityStatus) + `}}`

							bulkIdentitiesStatusRequest = bulkIdentitiesStatusRequest + identityStatusUpdateMetadata + "\n" + identityStatusUpdateDoc + "\n"
							recordsCount++

							if recordsCount > MAX_RECORDS {
								if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
									return err
								}
								logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

								recordsCount = 0
								bulkIdentitiesStatusRequest = ""
							}
						}
					}
				}
			}
			nameList = make(map[string][]string)
		}

		searchRctxDocIds := make([]string, 0)
		for rctxID := range searchRctxForIdentityStatus {
			searchRctxDocIds = append(searchRctxDocIds, rctxID)
			if len(searchRctxDocIds) >= MAX_RECORDS {
				rctxDocs, err := resource.FetchRctxDocsFromIDs(searchRctxDocIds, resourceContext)
				if err != nil {
					continue
				}

				for _, rctx := range rctxDocs {
					if identitiesDocs, ok := searchRctxForIdentityStatus[rctx.ID]; ok {
						for _, identitiesDoc := range identitiesDocs {
							identityStatus := GetIdentityStatusFromRctx(rctx, identitiesDoc.IdentityID)

							identityStatusUpdateMetadata := `{"update": {"_id": "` + identitiesDoc.ID + `"}}`
							identityStatusUpdateDoc := `{"doc":{"identityStatus":` + fmt.Sprint(identityStatus) + `}}`

							bulkIdentitiesStatusRequest = bulkIdentitiesStatusRequest + identityStatusUpdateMetadata + "\n" + identityStatusUpdateDoc + "\n"
							recordsCount++

							if recordsCount > MAX_RECORDS {
								if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
									return err
								}
								logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

								recordsCount = 0
								bulkIdentitiesStatusRequest = ""
							}
						}
					}
				}
			}
		}

		if len(searchRctxDocIds) > 0 {

			rctxDocs, err := resource.FetchRctxDocsFromIDs(searchRctxDocIds, resourceContext)
			if err != nil {
				continue
			}

			for _, rctx := range rctxDocs {

				if identitiesDocs, ok := searchRctxForIdentityStatus[rctx.ID]; ok {
					for _, identitiesDoc := range identitiesDocs {

						identityStatus := GetIdentityStatusFromRctx(rctx, identitiesDoc.IdentityID)

						identityStatusUpdateMetadata := `{"update": {"_id": "` + identitiesDoc.ID + `"}}`
						identityStatusUpdateDoc := `{"doc":{"identityStatus":` + fmt.Sprint(identityStatus) + `}}`

						bulkIdentitiesStatusRequest = bulkIdentitiesStatusRequest + identityStatusUpdateMetadata + "\n" + identityStatusUpdateDoc + "\n"
						recordsCount++

						if recordsCount > MAX_RECORDS {
							if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
								return err
							}
							logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

							recordsCount = 0
							bulkIdentitiesStatusRequest = ""
						}
					}
				}
			}
		}

		if recordsCount > 0 {
			if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesStatusRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Identity type bulk API Successful for ", []string{resourceContext.TenantID}, recordsCount)

		}
	}

	return nil
}

func updateIdentityStatusBasedOnRctxItem(resourceContext *rcontext.ResourceContext, identityId string, isServiceIdentity bool) (identityStatus int, ok bool) {

	if addr, err := common.ParseAddress(identityId); err == nil {
		// human/ nonhuman email/ service account

		if common.IsServiceAccountEmail(addr.Address) {
			identityStatus = common.VALID_IDENTITY_STATUS
		} else {
			if isNonHuman, ok := resourceContext.GetAliasUpdate(addr.Address); ok {
				identityStatus = DetermineIdentityStatusForEmail(resourceContext, addr.Address, isNonHuman)
				return identityStatus, true
			}
		}
	} else if isServiceIdentity {
		// service identity
		identityStatus = common.VALID_IDENTITY_STATUS
		return identityStatus, true
	}

	return
}

func deriveIdentityIdFromRctxItem(rctx *common.ResourceContextItem) (identityId string, serviceIdentity bool) {
	nonHumanIdentitySuffixes := []string{
		contextutils.AWSSERVICE_USER_SUFFIX,
		contextutils.ACCOUNT_USER_SUFFIX,
		contextutils.IAM_USER_SUFFIX,
		contextutils.IAM_ROLE_SUFFIX,
		contextutils.APP_USER_SUFFIX,
		contextutils.SERVICEACCOUNT_USER_SUFFIX,
	}

	if len(rctx.ChildIdentityID) > 0 {
		identityId = rctx.ChildIdentityID
	} else if len(rctx.IdentityId) > 0 {
		identityId = rctx.IdentityId
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
			serviceIdentity = true
		}
	} else if strings.Contains(rctx.Name, "<") && strings.Contains(rctx.Name, ">") {
		if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {

			serviceIdentity = true
			identityId = common.RemoveSuffixes(rctx.Name, nonHumanIdentitySuffixes)
		} else if strings.Contains(rctx.Name, "@") {
			if addr, err := common.ParseAddress(rctx.Name); err == nil {
				identityId = addr.Address
			}
		}
	} else {
		// for identity id's with only name
		identityId = rctx.Name
	}

	// some identities like role, etc. might have uppercase characters and space.
	identityId = strings.ToLower(strings.ReplaceAll(identityId, " ", ""))

	return
}
