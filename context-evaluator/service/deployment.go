package service

import (
	analyzerCommon "github.com/precize/context-evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveDeploymentContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedDeployment) <= 0 && len(rctxInsertDoc.DerivedDeployment) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.DEPLOYMENT_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedDeployment) > 0 {
		for _, definedDeployment := range rctxInsertDoc.DefinedDeployment {
			customerAddedContext[definedDeployment.IdentityId] = struct{}{}
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.DEPLOYMENT_AllContextDerived)
		return
	} else {
		for deployment := range customerAddedContext {
			foundDeployment := false
			addnDeploymentDerived := false

			for _, definedDeployment := range rctxInsertDoc.DefinedDeployment {
				if definedDeployment.IdentityId == deployment {
					foundDeployment = true
					break
				} else {
					addnDeploymentDerived = true
				}
			}

			if foundDeployment {
				continue
			}

			for _, derivedDeployment := range rctxInsertDoc.DerivedDeployment {
				if derivedDeployment.IdentityId == deployment {
					foundDeployment = true
					break
				} else {
					addnDeploymentDerived = true
				}
			}

			if !foundDeployment && (len(rctxInsertDoc.DefinedDeployment) > 0 || len(rctxInsertDoc.DerivedDeployment) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.DEPLOYMENT_ContradictoryContext)
				return
			}

			if !addnDeploymentDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.DEPLOYMENT_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.DEPLOYMENT_ExtraContextAdded)
	return
}
