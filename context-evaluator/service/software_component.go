package service

import (
	analyzerCommon "github.com/precize/context-evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveSoftwareComponentContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedSoftware) <= 0 && len(rctxInsertDoc.DerivedSoftware) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SOFTWARE_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedSoftware) > 0 {
		for _, definedSoftware := range rctxInsertDoc.DefinedSoftware {
			customerAddedContext[definedSoftware.IdentityId] = struct{}{}
			break
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SOFTWARE_AllContextDerived)
		return
	} else {
		for software := range customerAddedContext {
			foundSoftware := false
			addnSoftwareDerived := false

			for _, definedSoftware := range rctxInsertDoc.DefinedSoftware {
				if definedSoftware.IdentityId == software {
					foundSoftware = true
					break
				} else {
					addnSoftwareDerived = true
				}
			}

			if foundSoftware {
				continue
			}

			for _, derivedSoftware := range rctxInsertDoc.DerivedSoftware {
				if derivedSoftware.IdentityId == software {
					foundSoftware = true
					break
				} else {
					addnSoftwareDerived = true
				}
			}

			if !foundSoftware && (len(rctxInsertDoc.DefinedSoftware) > 0 || len(rctxInsertDoc.DerivedSoftware) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SOFTWARE_ContradictoryContext)
				return
			}

			if !addnSoftwareDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SOFTWARE_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SOFTWARE_ExtraContextAdded)
	return
}
