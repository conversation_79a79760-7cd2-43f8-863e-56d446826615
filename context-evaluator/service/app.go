package service

import (
	"strings"

	"github.com/precize/common/context"
	analyzerCommon "github.com/precize/context-evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveAppContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedApp) <= 0 && len(rctxInsertDoc.DerivedApp) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.APP_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedApp) > 0 {
		for _, definedApp := range rctxInsertDoc.DefinedApp {
			if strings.Contains(definedApp.Type, contextutils.TAG_PREFIX) {
				customerAddedContext[definedApp.IdentityId] = struct{}{}
			}
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.APP_AllContextDerived)
		return
	} else {
		for app := range customerAddedContext {
			foundApp := false
			addnAppDerived := false

			for _, definedApp := range rctxInsertDoc.DefinedApp {
				if !strings.Contains(definedApp.Type, contextutils.TAG_PREFIX) {
					if definedApp.IdentityId == app {
						foundApp = true
						break
					} else {
						addnAppDerived = true
					}
				}
			}

			if foundApp {
				continue
			}

			for _, derivedApp := range rctxInsertDoc.DerivedApp {
				if derivedApp.IdentityId == app {
					foundApp = true
					break
				} else {
					addnAppDerived = true
				}
			}

			if !foundApp && (len(rctxInsertDoc.DefinedApp) > 0 || len(rctxInsertDoc.DerivedApp) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.APP_ContradictoryContext)
				return
			}

			if !addnAppDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.APP_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.APP_ExtraContextAdded)
	return

}
