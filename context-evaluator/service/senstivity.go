package service

import (
	analyzerCommon "github.com/precize/context-evaluator/common"
)

func (rtcxItem *ResourceContextWrapper) DeriveSensitivityContextCategory() {

	rctxInsertDoc := rtcxItem.ResourceContextInsertDoc
	customerAddedContext := map[string]struct{}{}

	if len(rctxInsertDoc.DefinedSensitivity) <= 0 && len(rctxInsertDoc.DerivedSensitivity) <= 0 && len(rctxInsertDoc.InheritedSensitivity) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SENSITIVITY_NoContextDerived)
		return
	}

	if len(rctxInsertDoc.DefinedSensitivity) > 0 {
		for _, definedSensitivity := range rctxInsertDoc.DefinedSensitivity {
			customerAddedContext[definedSensitivity.IdentityId] = struct{}{}
			break
		}
	}

	if len(customerAddedContext) <= 0 {
		rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SENSITIVITY_AllContextDerived)
		return
	} else {

		for sensitivity := range customerAddedContext {
			foundSensitivity := false
			addnSensitivityDerived := false

			for _, definedSensitivity := range rctxInsertDoc.DefinedSensitivity {
				if definedSensitivity.IdentityId == sensitivity {
					foundSensitivity = true
					break
				} else {
					addnSensitivityDerived = true
				}
			}

			if foundSensitivity {
				continue
			}

			for _, derivedSensitivity := range rctxInsertDoc.DerivedSensitivity {
				if derivedSensitivity.IdentityId == sensitivity {
					foundSensitivity = true
					break
				} else {
					addnSensitivityDerived = true
				}
			}

			if foundSensitivity {
				continue
			}

			for _, inheritedSensitivity := range rctxInsertDoc.InheritedSensitivity {
				if inheritedSensitivity.IdentityId == sensitivity {
					foundSensitivity = true
					break
				} else {
					addnSensitivityDerived = true
				}
			}

			if !foundSensitivity && (len(rctxInsertDoc.DefinedSensitivity) > 0 || len(rctxInsertDoc.DerivedSensitivity) > 0) {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SENSITIVITY_ContradictoryContext)
				return
			}

			if !addnSensitivityDerived {
				rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SENSITIVITY_NOADDNCONTEXTDERIVED)
				return
			}
		}
	}

	rctxInsertDoc.ContextLabels = append(rctxInsertDoc.ContextLabels, analyzerCommon.SENSITIVITY_ExtraContextAdded)
	return
}
