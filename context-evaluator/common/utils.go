package common

const (
	ExtraContextAdded    string = "AddnContextAdded"
	AllContextDerived    string = "AllContextDerived"
	NOADDNCONTEXTDERIVED string = "NoAddnContextDerived"
	ContradictoryContext string = "ContradictoryContext"
	NoContextDerived     string = "NoContextDerived"

	// Owner Context Categories
	OWNER_ExtraContextAdded    string = "OWNER_AddnContextAdded"
	OWNER_NOADDNCONTEXTDERIVED string = "OWNER_NoAddnContextDerived"
	OWNER_AllContextDerived    string = "OWNER_AllContextDerived"
	OWNER_ContradictoryContext string = "OWNER_ContradictoryContext"
	OWNER_NoContextDerived     string = "OWNER_NoContextDerived"

	// Team Context Categories
	TEAM_ExtraContextAdded    string = "TEAM_AddnContextAdded"
	TEAM_NOADDNCONTEXTDERIVED string = "TEAM_NoAddnContextDerived"
	TEAM_AllContextDerived    string = "TEAM_AllContextDerived"
	TEAM_ContradictoryContext string = "TEAM_ContradictoryContext"
	TEAM_NoContextDerived     string = "TEAM_NoContextDerived"

	// App Context Categories
	APP_ExtraContextAdded    string = "APP_AddnContextAdded"
	APP_NOADDNCONTEXTDERIVED string = "APP_NoAddnContextDerived"
	APP_AllContextDerived    string = "APP_AllContextDerived"
	APP_ContradictoryContext string = "APP_ContradictoryContext"
	APP_NoContextDerived     string = "APP_NoContextDerived"

	// Software Context Categories
	SOFTWARE_ExtraContextAdded    string = "SOFTWARE_AddnContextAdded"
	SOFTWARE_NOADDNCONTEXTDERIVED string = "SOFTWARE_NoAddnContextDerived"
	SOFTWARE_AllContextDerived    string = "SOFTWARE_AllContextDerived"
	SOFTWARE_ContradictoryContext string = "SOFTWARE_ContradictoryContext"
	SOFTWARE_NoContextDerived     string = "SOFTWARE_NoContextDerived"

	// Environment Context Categories
	ENV_ExtraContextAdded    string = "ENV_AddnContextAdded"
	ENV_NOADDNCONTEXTDERIVED string = "ENV_NoAddnContextDerived"
	ENV_AllContextDerived    string = "ENV_AllContextDerived"
	ENV_ContradictoryContext string = "ENV_ContradictoryContext"
	ENV_NoContextDerived     string = "ENV_NoContextDerived"

	// Deployment Context Categories
	DEPLOYMENT_ExtraContextAdded    string = "DEPLOYMENT_AddnContextAdded"
	DEPLOYMENT_NOADDNCONTEXTDERIVED string = "DEPLOYMENT_NoAddnContextDerived"
	DEPLOYMENT_AllContextDerived    string = "DEPLOYMENT_AllContextDerived"
	DEPLOYMENT_ContradictoryContext string = "DEPLOYMENT_ContradictoryContext"
	DEPLOYMENT_NoContextDerived     string = "DEPLOYMENT_NoContextDerived"

	// Sensitivity Context Categories
	SENSITIVITY_ExtraContextAdded    string = "SENSITIVITY_AddnContextAdded"
	SENSITIVITY_NOADDNCONTEXTDERIVED string = "SENSITIVITY_NoAddnContextDerived"
	SENSITIVITY_AllContextDerived    string = "SENSITIVITY_AllContextDerived"
	SENSITIVITY_ContradictoryContext string = "SENSITIVITY_ContradictoryContext"
	SENSITIVITY_NoContextDerived     string = "SENSITIVITY_NoContextDerived"

	// Compliance Context Categories
	COMPLIANCE_ExtraContextAdded    string = "COMPLIANCE_AddnContextAdded"
	COMPLIANCE_AllContextDerived    string = "COMPLIANCE_AllContextDerived"
	COMPLIANCE_NOADDNCONTEXTDERIVED string = "COMPLIANCE_NoAddnContextDerived"
	COMPLIANCE_ContradictoryContext string = "COMPLIANCE_ContradictoryContext"
	COMPLIANCE_NoContextDerived     string = "COMPLIANCE_NoContextDerived"

	// TTL Context Categories
	TTL_ExtraContextAdded    string = "TTL_AddnContextAdded"
	TTL_AllContextDerived    string = "TTL_AllContextDerived"
	TTL_ContradictoryContext string = "TTL_ContradictoryContext"
	TTL_NOADDNCONTEXTDERIVED string = "TTL_NoAddnContextDerived"
	TTL_NoContextDerived     string = "TTL_NoContextDerived"
)
