package incidents

import (
	"encoding/json"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/elastic"
)

var (
	excludeVirtualResourcesQuery = `,"must_not":[{"terms":{"entityType.keyword":["` +
		common.AZURE_LOCATION_RESOURCE_TYPE + `","` + common.AZURE_ASSIGNEDROLE_RESOURCE_TYPE + `","` + common.AZURE_ROLEASSIGNMENT_RESOURCE_TYPE + `","` + common.AZURE_GROUPS_RESOURCE_TYPE + `","` + common.AZURE_POLICYSTATE_RESOURCE_TYPE + `","` +
		common.AZURE_POLICYDEFINITION_RESOURCE_TYPE + `","` + common.AWS_REGION_RESOURCE_TYPE + `","` + common.GCP_REGION_RESOURCE_TYPE + `","` + common.GCP_CONSTRAINT_RESOURCE_TYPE + `","` + common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE + `","` + common.AWS_PERSONALIZERECIPE_RESOURCE_TYPE + `","` + common.AZURE_USEROWNER_RESOURCE_TYPE + `"]}}]`

	excludeVirtualResourcesEntTypeQuery = `{"terms":{"entityType.keyword":["` +
		common.AZURE_LOCATION_RESOURCE_TYPE + `","` + common.AZURE_ASSIGNEDROLE_RESOURCE_TYPE + `","` + common.AZURE_ROLEASSIGNMENT_RESOURCE_TYPE + `","` + common.AZURE_GROUPS_RESOURCE_TYPE + `","` + common.AZURE_POLICYSTATE_RESOURCE_TYPE + `","` +
		common.AZURE_POLICYDEFINITION_RESOURCE_TYPE + `","` + common.AWS_REGION_RESOURCE_TYPE + `","` + common.GCP_REGION_RESOURCE_TYPE + `","` + common.GCP_CONSTRAINT_RESOURCE_TYPE + `","` + common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE + `","` + common.AWS_PERSONALIZERECIPE_RESOURCE_TYPE + `","` + common.AZURE_USEROWNER_RESOURCE_TYPE + `"]}}`
)

func fetchCriteriaValues(tc *Criteria, crsDoc common.CloudResourceStoreDoc, lastCollectedAt string) (
	tenantVal tenantCriteriaValue, accountVal accountCriteriaValue, resourceVal resourceCriteriaValue, err error) {

	accountID := crsDoc.AccountID

	if crsDoc.EntityType == common.AZURE_SUBSCRIPTION_RESOURCE_TYPE {
		accountID = crsDoc.EntityID
	}

	tenantVal, ok := tc.LoadTenantValues(crsDoc.TenantID)
	if !ok {
		tenantVal = tenantCriteriaValue{
			accountCosts: make(map[string]float64),
		}
	}

	accountVal, ok = tc.LoadAccountValues(accountID)
	if !ok {
		accountVal = accountCriteriaValue{
			costCenters: []string{},
		}
	}

	var (
		serviceID           string
		impactResourceTypes []string
	)

	for _, serviceIDInt := range crsDoc.ServiceID {

		switch serviceIDInt {
		case common.AWS_SERVICE_ID_INT:
			impactResourceTypes = common.AWSImpactResource
			serviceID = common.AWS_SERVICE_ID
		case common.AZURE_SERVICE_ID_INT:
			impactResourceTypes = common.AZUREImpactResource
			serviceID = common.AZURE_SERVICE_ID
		case common.GCP_SERVICE_ID_INT:
			impactResourceTypes = common.GCPImpactResource
			serviceID = common.GCP_SERVICE_ID
		case common.OPENAI_SERVICE_ID_INT:
			serviceID = common.OPENAI_SERVICE_ID
		}

		if len(serviceID) > 0 {
			break
		}
	}

	// Check if tenant values was already evaluated
	if tenantVal.resourceCount <= 0 {
		tenantResourcesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + crsDoc.TenantID + `"}},{"match":{"serviceId":"` + serviceID + `"}},{"term":{"isDeleted":"false"}}]` + excludeVirtualResourcesQuery + `}}}`

		tenantVal.resourceCount, err = elastic.GetIndexCount(elastic.CLOUD_RESOURCE_STORE_INDEX, tenantResourcesQuery)
		if err != nil {
			return
		}

	}

	if len(tenantVal.accountCosts) <= 0 {
		accountCosts := make(map[string]float64, 0)

		costQuery := `{"query":{"bool":{"must":[{"bool":{"should":[{"term":{"isDeleted":{"value":false,"boost":1}}}],"adjust_pure_negative":true,"boost":1}},{"term":{"tenantId.keyword":{"value":"` + crsDoc.TenantID + `","boost":1}}},{"bool":{"adjust_pure_negative":true,"boost":1}},{"term":{"serviceId":{"value":` + serviceID + `,"boost":1}}}],"must_not":[{"terms":{"entityType.keyword":["Region","Constraint","AssignedRole","AzureRoleAssignment","groups","PolicyDefinition","PolicyState","OpenAICF","OpenAICFBL","PersonalizeRecipe","Budget","UserOwnedResource"],"boost":1}}],"adjust_pure_negative":true,"boost":1}},"aggregations":{"COST_ACCOUNT":{"terms":{"field":"accountId.keyword","size":20,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"monthlyCost":"desc"},{"_key":"asc"}]},"aggregations":{"monthlyCost":{"sum":{"field":"monthlyCost"}}}}}}`
		costAggsResp, er := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, costQuery)
		if er != nil {
			err = er
			return
		}

		if costAggs, ok := costAggsResp["COST_ACCOUNT"].(map[string]any); ok {
			if costBuckets, ok := costAggs["buckets"].([]any); ok {
				for _, costBucket := range costBuckets {
					if costBucketMap, ok := costBucket.(map[string]any); ok {
						if accountId, ok := costBucketMap["key"].(string); ok {
							if monthlyCost, ok := costBucketMap["monthlyCost"].(map[string]any); ok {
								accountCosts[accountId] = monthlyCost["value"].(float64)
							}
						}
					}
				}
			}
		}

		costQuery = `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":{"value":"` + crsDoc.TenantID + `","boost":1}}},{"term":{"collectedAt":{"value":` + lastCollectedAt + `,"boost":1}}}],"adjust_pure_negative":true,"boost":1}},"aggregations":{"COST_ACCOUNT":{"terms":{"field":"accountId.keyword","size":20,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"COST":"desc"},{"_key":"asc"}]},"aggregations":{"COST":{"sum":{"field":"cost"}}}}}}`
		costAggsResp, err = elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_ASSET_COSTS_INDEX}, costQuery)
		if err != nil {
			return
		}

		if costAggs, ok := costAggsResp["COST_ACCOUNT"].(map[string]any); ok {
			if costBuckets, ok := costAggs["buckets"].([]any); ok {
				for _, costBucket := range costBuckets {
					if costBucketMap, ok := costBucket.(map[string]any); ok {
						if accountId, ok := costBucketMap["key"].(string); ok {
							if monthlyCost, ok := costBucketMap["COST"].(map[string]any); ok {
								accountCosts[accountId] = monthlyCost["value"].(float64)
							}
						}
					}
				}
			}
		}

		tenantVal.accountCosts = accountCosts

		tenantVal.identityHeroStats = make(map[string]map[string]any)
		cloudIdentityQuery := `{"query":{"bool":{"must":[{"terms":{"types.keyword":["AD_USER","SERVICE_ACCOUNT","AWS_IAM_ROLE","GRAPH_APPLICATION","OKTA_USER","APP_REGISTRATION","USER","GCP_USER","TRUSTED_ENTITY","SSO_USER","ROOT_USER","OPEN_AI_SERVICE_ACCOUNT","OPEN_AI_USER"]}},{"terms":{"hsDbKeys.keyword":["exUser","partnerExUser","nonIamExUsers","overPrivileged","personalEmail","MFADisabled","identityOwningMoreThanOneEnterpriseApplication","serviceIdentityWithoutActiveKeys","serviceIdentityWithStaleKeys","exEmployeeOwningServiceIdentity","improperRootAccountUsage"]}},{"wildcard":{"tenantId.keyword":"` + crsDoc.TenantID + `"}},{"term":{"deleted":"false"}}],"must_not":[],"should":[]}},"from":0,"size":0,"sort":[],"aggs":{"resource":{"terms":{"field":"entityId.keyword","size":10000},"aggs":{"entity_types":{"top_hits":{"size":1,"_source":["types"]}},"ex_user":{"top_hits":{"size":1,"_source":["exUser"]}},"category":{"top_hits":{"size":1,"_source":["category"]}},"identityType":{"top_hits":{"size":1,"_source":["identityType"]}},"hero_stats":{"terms":{"field":"hsDbKeys.keyword","size":10000}}}}}}`
		cloudIdentityAggsResp, er := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_IDENTITY_INDEX}, cloudIdentityQuery)
		if er != nil {
			err = er
			return
		}

		type Source struct {
			ExUser       bool     `json:"exUser"`
			Types        []string `json:"types"`
			Category     int      `json:"category"`
			IdentityType string   `json:"identityType"`
		}

		type TopHitsResponse struct {
			Hits struct {
				Hits []struct {
					Source Source `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		}

		type ResourceBucket struct {
			Key          string          `json:"key"`
			EntityTypes  TopHitsResponse `json:"entity_types"`
			ExUser       TopHitsResponse `json:"ex_user"`
			Category     TopHitsResponse `json:"category"`
			IdentityType TopHitsResponse `json:"identityType"`
			HeroStats    struct {
				Buckets []struct {
					Key string `json:"key"`
				} `json:"buckets"`
			} `json:"hero_stats"`
		}

		type CloudIdentityHSResponse struct {
			ResourceTypes struct {
				Buckets []ResourceBucket `json:"buckets"`
			} `json:"resource"`
		}

		var esResp CloudIdentityHSResponse
		respBytes, er := json.Marshal(cloudIdentityAggsResp)
		if er != nil {
			err = er
			return
		}

		if err = json.Unmarshal(respBytes, &esResp); err != nil {
			return
		}

		tenantVal.identityHeroStats = make(map[string]map[string]any)
		for _, bucket := range esResp.ResourceTypes.Buckets {
			identityID := bucket.Key
			tenantVal.identityHeroStats[identityID] = make(map[string]any)

			heroStats := make(map[string]struct{})
			for _, heroStat := range bucket.HeroStats.Buckets {
				heroStats[heroStat.Key] = struct{}{}
			}
			tenantVal.identityHeroStats[identityID]["heroStats"] = heroStats

			isExUser := false
			if len(bucket.ExUser.Hits.Hits) > 0 {
				isExUser = bucket.ExUser.Hits.Hits[0].Source.ExUser
			}
			tenantVal.identityHeroStats[identityID]["isExUser"] = isExUser

			if len(bucket.EntityTypes.Hits.Hits) > 0 {
				tenantVal.identityHeroStats[identityID]["identityTypes"] = bucket.EntityTypes.Hits.Hits[0].Source.Types
			}

			if len(bucket.Category.Hits.Hits) > 0 {
				tenantVal.identityHeroStats[identityID]["category"] = bucket.Category.Hits.Hits[0].Source.Category
			}

			if len(bucket.IdentityType.Hits.Hits) > 0 {
				tenantVal.identityHeroStats[identityID]["identityType"] = bucket.IdentityType.Hits.Hits[0].Source.IdentityType
			}
		}
	}

	tc.StoreTenantValues(crsDoc.TenantID, tenantVal)

	// Check if account values was already evaluated
	if _, ok := tc.LoadAccountValues(accountID); !ok {

		if serviceID == "2000" && len(crsDoc.ResourceGroup) > 0 && crsDoc.EntityType != "ResourceGroup" {
			accountID = crsDoc.ResourceGroup
		}

		accountVal, err = fetchAccountValues(accountID, crsDoc.TenantID, serviceID, crsDoc.EntityType, impactResourceTypes, lastCollectedAt)
		if err != nil {
			return
		}
		tc.StoreAccountValues(accountID, accountVal)
	}

	// Check if resource values was already evaluated
	resourceVal, ok = tc.LoadResourceValue(crsDoc.EntityID)
	if !ok {
		resourceQuery := `
		{
			"query": {
			  "bool": {
				"must": [
				  {
					"term": {
					  "resources.resourceName.keyword": "` + crsDoc.EntityID + `"
					}
				  },
				  {
					"term": {
					  "readOnly":"false"
					}
				  }
				],
				"must_not": [],
				"should": []
			  }
			}
		  }
		`

		resourceVal.activitiesCount, err = elastic.GetIndexCount(elastic.CLOUD_ACTIVITY_INDEX, resourceQuery)
		if err != nil {
			return
		}

		tc.StoreResourceValue(crsDoc.EntityID, resourceVal)
	}

	return
}

func fetchAccountValues(accountID, tenantID, serviceID, entityType string, impactResourceTypes []string, lastCollectedAt string) (accountCriteriaValue, error) {
	var accountVal accountCriteriaValue
	accountVal.costCenters = []string{}
	accountTypeQuery := "accountId"

	if serviceID == "2000" {
		switch entityType {
		case "Tenant", "Subscription", "ResourceGroup", "MgtGroup":

		default:
			accountTypeQuery = "resourceGroup"
		}
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	errChan := make(chan error, 3)

	resourceResourcesQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"isDeleted":"false"}}]` + excludeVirtualResourcesQuery + `}}}`
	resourceCount, err := elastic.GetIndexCount(elastic.CLOUD_RESOURCE_STORE_INDEX, resourceResourcesQuery)
	if err != nil {
		return accountVal, err
	}
	mu.Lock()
	accountVal.resourceCount = resourceCount
	mu.Unlock()

	impactResourcesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"term":{"isDeleted":"false"}},{"terms":{"entityType.keyword":["` + strings.Join(impactResourceTypes, `","`) + `"]}}]` + excludeVirtualResourcesQuery + `}}}`
	impactResourcesCount, err := elastic.GetIndexCount(elastic.CLOUD_RESOURCE_STORE_INDEX, impactResourcesQuery)
	if err != nil {
		return accountVal, err
	}
	mu.Lock()
	accountVal.impactResourcesCount = impactResourcesCount
	mu.Unlock()

	currenTime := time.Now().UTC()
	activityStartTime := elastic.DateTime(currenTime.AddDate(0, -2, 0))
	activityEndTime := elastic.DateTime(currenTime)

	accountActivitiesQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"range":{"eventTime":{"gt":"` + activityStartTime + `","lte":"` + activityEndTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"isUpdateEvent":"true"}},{"term":{"readOnly":"false"}}]` + excludeVirtualResourcesQuery + `}}}`
	activitiesCount, err := elastic.GetIndexCount(elastic.CLOUD_ACTIVITY_INDEX, accountActivitiesQuery)
	if err != nil {
		return accountVal, err
	}
	mu.Lock()
	accountVal.activitiesCount = activitiesCount
	mu.Unlock()

	noOwnerResourcesInAccountQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"owner":"NONE"}},{"term":{"isDeleted":"false"}}]` + excludeVirtualResourcesQuery + `}}}`
	noOwnersCount, err := elastic.GetIndexCount(elastic.CLOUD_RESOURCE_STORE_INDEX, noOwnerResourcesInAccountQuery)
	if err != nil {
		return accountVal, err
	}
	mu.Lock()
	accountVal.noOwnersCount = noOwnersCount
	mu.Unlock()

	wg.Add(1)
	go func() {
		defer wg.Done()
		ownerAggsQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"isDeleted":"false"}}]` + excludeVirtualResourcesQuery + `}},"from":0,"size":0,"sort":[],"aggs":{"owner_aggregation":{"terms":{"field":"owner.keyword","size":10000}}}}`
		ownerAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, ownerAggsQuery)
		if err != nil {
			errChan <- err
			return
		}

		mu.Lock()
		defer mu.Unlock()
		if ownerAggs, ok := ownerAggsResp["owner_aggregation"].(map[string]any); ok {
			if resourceBuckets, ok := ownerAggs["buckets"].([]any); ok {
				for _, resourceBucket := range resourceBuckets {
					if resourceBucketMap, ok := resourceBucket.(map[string]any); ok {
						if ownerName, ok := resourceBucketMap["key"].(string); ok {
							if docCount, ok := resourceBucketMap["doc_count"].(float64); ok {
								if !strings.Contains(ownerName, "NONE") {
									accountVal.totalOwnersCount += int64(docCount)
									accountVal.uniqueOwnersCount += 1
								}

								if strings.Contains(ownerName, contextutils.EX_EMPLOYEE_PREFIX) {
									accountVal.exEmployeesCount += int64(docCount)
								}
							}
						}
					}
				}
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		envAggsQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"isDeleted":"false"}}]` + excludeVirtualResourcesQuery + `}},"from":0,"size":0,"sort":[],"aggs":{"envs_aggregation":{"terms":{"field":"environment.keyword","size":10000}}}}`
		envAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, envAggsQuery)
		if err != nil {
			errChan <- err
			return
		}

		mu.Lock()
		defer mu.Unlock()
		if envAggs, ok := envAggsResp["envs_aggregation"].(map[string]any); ok {
			if resourceBuckets, ok := envAggs["buckets"].([]any); ok {
				for _, resourceBucket := range resourceBuckets {
					if resourceBucketMap, ok := resourceBucket.(map[string]any); ok {
						if envType, ok := resourceBucketMap["key"].(string); ok {
							if docCount, ok := resourceBucketMap["doc_count"].(float64); ok {
								if envType != "NONE" {
									accountVal.totalEnvCount += int64(docCount)
								}

								if strings.Contains(envType, contextutils.PROD_ENV) {
									accountVal.prodEnvCount += int64(docCount)
								}
							}
						}
					}
				}
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		costCenterQuery := `{"query":{"bool":{"must":[{"match":{"` + accountTypeQuery + `.keyword":"` + accountID + `"}},{"term":{"collectedAt":"` + lastCollectedAt + `"}}],"must_not":[],"should":[]}},"size":0,"aggs":{"unique_cost_centers":{"terms":{"field":"costCenter.keyword","size":10000,"min_doc_count":1}}}}`
		costCenterAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCES_INDEX}, costCenterQuery)
		if err != nil {
			errChan <- err
			return
		}

		mu.Lock()
		defer mu.Unlock()
		if costCAggs, ok := costCenterAggsResp["unique_cost_centers"].(map[string]any); ok {
			if costCBuckets, ok := costCAggs["buckets"].([]any); ok {
				for _, costCBucket := range costCBuckets {
					if costCBucketMap, ok := costCBucket.(map[string]any); ok {
						if costCenter, ok := costCBucketMap["key"].(string); ok {
							if costCenter != "NONE" {
								accountVal.costCenters = append(accountVal.costCenters, costCenter)
							}
						}
					}
				}
			}
		}
	}()

	wg.Wait()

	close(errChan)
	for err := range errChan {
		if err != nil {
			return accountVal, err
		}
	}

	return accountVal, nil
}
